{"name": "test-case-automation-frontend", "version": "1.0.0", "description": "测试用例自动化平台前端", "private": true, "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@ant-design/x": "^1.4.0", "@element-plus/icons-vue": "^2.3.1", "@types/d3": "^7.4.3", "@vueuse/core": "^10.7.0", "axios": "^1.6.2", "d3": "^7.9.0", "dayjs": "^1.11.10", "echarts": "^5.4.3", "element-plus": "^2.4.4", "file-saver": "^2.0.5", "lodash-es": "^4.17.21", "mermaid": "^10.6.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-echarts": "^6.6.1", "vue-router": "^4.2.5", "xlsx": "^0.18.5"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/lodash-es": "^4.17.12", "@types/node": "^20.10.6", "@types/nprogress": "^0.2.3", "@typescript-eslint/eslint-plugin": "^6.16.0", "@typescript-eslint/parser": "^6.16.0", "@vitejs/plugin-vue": "^4.5.2", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "sass": "^1.69.7", "typescript": "^5.3.3", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.10", "vue-tsc": "^1.8.25"}}