# 应用配置
VITE_APP_TITLE=测试用例自动化平台
VITE_APP_VERSION=1.0.0

# API配置
VITE_API_BASE_URL=http://localhost:8000
VITE_API_TIMEOUT=60000

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=true

# 上传配置
VITE_UPLOAD_MAX_SIZE=500
VITE_UPLOAD_ALLOWED_TYPES=.jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt,.json,.yaml,.yml,.sql,.mp4,.avi,.mov,.wmv,.flv,.webm

# SSE配置
VITE_SSE_RECONNECT_ATTEMPTS=5
VITE_SSE_RECONNECT_DELAY=1000

# 主题配置
VITE_DEFAULT_THEME=light
VITE_DEFAULT_LANGUAGE=zh-CN
