// 工具类样式系统
// 原子化CSS工具类

// ==================== 布局工具类 ====================
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.hidden { display: none; }
.grid { display: grid; }
.table { display: table; }

// Flex方向
.flex-row { flex-direction: row; }
.flex-row-reverse { flex-direction: row-reverse; }
.flex-col { flex-direction: column; }
.flex-col-reverse { flex-direction: column-reverse; }

// Flex换行
.flex-wrap { flex-wrap: wrap; }
.flex-wrap-reverse { flex-wrap: wrap-reverse; }
.flex-nowrap { flex-wrap: nowrap; }

// Flex对齐
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

// 常用组合
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

// ==================== 定位工具类 ====================
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }
.static { position: static; }

// 定位值
.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

// ==================== 尺寸工具类 ====================
.w-auto { width: auto; }
.w-full { width: 100%; }
.w-screen { width: 100vw; }
.w-min { width: min-content; }
.w-max { width: max-content; }
.w-fit { width: fit-content; }

.h-auto { height: auto; }
.h-full { height: 100%; }
.h-screen { height: 100vh; }
.h-min { height: min-content; }
.h-max { height: max-content; }
.h-fit { height: fit-content; }

// 最小/最大尺寸
.min-w-0 { min-width: 0; }
.min-w-full { min-width: 100%; }
.min-h-0 { min-height: 0; }
.min-h-full { min-height: 100%; }
.min-h-screen { min-height: 100vh; }

.max-w-none { max-width: none; }
.max-w-full { max-width: 100%; }
.max-w-screen { max-width: 100vw; }
.max-h-none { max-height: none; }
.max-h-full { max-height: 100%; }
.max-h-screen { max-height: 100vh; }

// ==================== 间距工具类 ====================
// 外边距
.m-0 { margin: 0; }
.m-auto { margin: auto; }
.mx-auto { margin-left: auto; margin-right: auto; }
.my-auto { margin-top: auto; margin-bottom: auto; }

// 内边距
.p-0 { padding: 0; }

// 生成间距类
@each $key, $value in (
  1: var(--spacing-1),
  2: var(--spacing-2),
  3: var(--spacing-3),
  4: var(--spacing-4),
  5: var(--spacing-5),
  6: var(--spacing-6),
  8: var(--spacing-8),
  10: var(--spacing-10),
  12: var(--spacing-12),
  16: var(--spacing-16),
  20: var(--spacing-20),
  24: var(--spacing-24)
) {
  // 外边距
  .m-#{$key} { margin: #{$value}; }
  .mt-#{$key} { margin-top: #{$value}; }
  .mr-#{$key} { margin-right: #{$value}; }
  .mb-#{$key} { margin-bottom: #{$value}; }
  .ml-#{$key} { margin-left: #{$value}; }
  .mx-#{$key} { margin-left: #{$value}; margin-right: #{$value}; }
  .my-#{$key} { margin-top: #{$value}; margin-bottom: #{$value}; }

  // 内边距
  .p-#{$key} { padding: #{$value}; }
  .pt-#{$key} { padding-top: #{$value}; }
  .pr-#{$key} { padding-right: #{$value}; }
  .pb-#{$key} { padding-bottom: #{$value}; }
  .pl-#{$key} { padding-left: #{$value}; }
  .px-#{$key} { padding-left: #{$value}; padding-right: #{$value}; }
  .py-#{$key} { padding-top: #{$value}; padding-bottom: #{$value}; }
}

// ==================== 文字工具类 ====================
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

// 字体大小
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }

// 字体粗细
.font-thin { font-weight: var(--font-weight-thin); }
.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }
.font-extrabold { font-weight: var(--font-weight-extrabold); }

// 行高
.leading-none { line-height: var(--line-height-none); }
.leading-tight { line-height: var(--line-height-tight); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }
.leading-loose { line-height: var(--line-height-loose); }

// 文字颜色
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-inverse { color: var(--text-inverse); }
.text-link { color: var(--text-link); }

// 文字装饰
.underline { text-decoration: underline; }
.line-through { text-decoration: line-through; }
.no-underline { text-decoration: none; }

// 文字变换
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

// 文字溢出
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis { text-overflow: ellipsis; }
.text-clip { text-overflow: clip; }

// ==================== 背景工具类 ====================
.bg-transparent { background-color: transparent; }
.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }

// ==================== 边框工具类 ====================
.border { border-width: var(--border-width); }
.border-0 { border-width: 0; }
.border-2 { border-width: var(--border-width-thick); }

.border-t { border-top-width: var(--border-width); }
.border-r { border-right-width: var(--border-width); }
.border-b { border-bottom-width: var(--border-width); }
.border-l { border-left-width: var(--border-width); }

// 边框颜色
.border-transparent { border-color: transparent; }
.border-current { border-color: currentColor; }
.border-gray { border-color: var(--border-color); }
.border-primary { border-color: var(--primary-500); }

// 圆角
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded { border-radius: var(--border-radius); }
.rounded-md { border-radius: var(--border-radius-md); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-xl { border-radius: var(--border-radius-xl); }
.rounded-2xl { border-radius: var(--border-radius-2xl); }
.rounded-full { border-radius: var(--border-radius-full); }

// ==================== 阴影工具类 ====================
.shadow-none { box-shadow: none; }
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
.shadow-inner { box-shadow: var(--shadow-inner); }

// ==================== 溢出工具类 ====================
.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }

.overflow-x-auto { overflow-x: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-x-visible { overflow-x: visible; }
.overflow-x-scroll { overflow-x: scroll; }

.overflow-y-auto { overflow-y: auto; }
.overflow-y-hidden { overflow-y: hidden; }
.overflow-y-visible { overflow-y: visible; }
.overflow-y-scroll { overflow-y: scroll; }

// ==================== 交互工具类 ====================
.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-help { cursor: help; }
.cursor-not-allowed { cursor: not-allowed; }

.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }
.select-auto { user-select: auto; }

.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

// ==================== 可见性工具类 ====================
.visible { visibility: visible; }
.invisible { visibility: hidden; }

.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

// ==================== 层级工具类 ====================
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
.z-auto { z-index: auto; }

// ==================== 过渡工具类 ====================
.transition-none { transition: none; }
.transition-all { transition: var(--transition-all); }
.transition-colors { transition: var(--transition-colors); }
.transition-opacity { transition: var(--transition-opacity); }
.transition-shadow { transition: var(--transition-shadow); }
.transition-transform { transition: var(--transition-transform); }

// ==================== 变换工具类 ====================
.transform { transform: translateX(0) translateY(0) rotate(0) skewX(0) skewY(0) scaleX(1) scaleY(1); }
.transform-none { transform: none; }

.scale-0 { transform: scale(0); }
.scale-50 { transform: scale(0.5); }
.scale-75 { transform: scale(0.75); }
.scale-90 { transform: scale(0.9); }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.scale-105 { transform: scale(1.05); }
.scale-110 { transform: scale(1.1); }
.scale-125 { transform: scale(1.25); }
.scale-150 { transform: scale(1.5); }
