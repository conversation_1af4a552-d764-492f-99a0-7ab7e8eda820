// 主题系统
// 支持明暗主题切换

// ==================== 暗色主题 ====================
.dark {
  // 重新定义颜色变量
  --bg-primary: var(--gray-900);
  --bg-secondary: var(--gray-800);
  --bg-tertiary: var(--gray-700);
  --bg-quaternary: var(--gray-600);

  --text-primary: var(--gray-50);
  --text-secondary: var(--gray-300);
  --text-tertiary: var(--gray-400);
  --text-quaternary: var(--gray-500);
  --text-disabled: var(--gray-600);

  --border-color: var(--gray-700);
  --border-color-light: var(--gray-600);
  --border-color-dark: var(--gray-500);

  // 调整阴影为暗色主题
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
  --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --shadow-md: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
  --shadow-xl: 0 25px 50px -12px rgb(0 0 0 / 0.5);

  // 状态背景色调整
  --bg-success: var(--success-900);
  --bg-warning: var(--warning-900);
  --bg-error: var(--error-900);
  --bg-info: var(--info-900);
}

// ==================== 高对比度主题 ====================
.high-contrast {
  --primary-color: #0066cc;
  --text-primary: #000000;
  --text-secondary: #333333;
  --bg-primary: #ffffff;
  --bg-secondary: #f5f5f5;
  --border-color: #000000;
  --border-width: 2px;
}

// ==================== 护眼主题 ====================
.eye-care {
  --bg-primary: #f7f3e9;
  --bg-secondary: #f0ead6;
  --bg-tertiary: #e8dcc0;
  --text-primary: #2d2d2d;
  --text-secondary: #4a4a4a;
  --border-color: #d4c5a9;
}

// ==================== 主题切换动画 ====================
.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

// 为所有元素添加主题切换动画
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

// ==================== 主题特定组件样式 ====================

// 暗色主题下的特殊样式
.dark {
  // 卡片组件
  .tc-card {
    background-color: var(--bg-primary);
    border-color: var(--border-color);

    &__header {
      background-color: var(--bg-secondary);
      border-bottom-color: var(--border-color-light);
    }

    &__footer {
      background-color: var(--bg-secondary);
      border-top-color: var(--border-color-light);
    }
  }

  // 按钮组件
  .tc-button {
    &--secondary {
      background-color: var(--bg-secondary);
      border-color: var(--border-color);
      color: var(--text-primary);

      &:hover:not(:disabled) {
        background-color: var(--bg-tertiary);
        border-color: var(--border-color-dark);
      }
    }

    &--ghost {
      color: var(--text-primary);

      &:hover:not(:disabled) {
        background-color: var(--bg-secondary);
      }
    }
  }

  // 输入框组件
  .tc-input {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);

    &::placeholder {
      color: var(--text-quaternary);
    }

    &:disabled {
      background-color: var(--bg-tertiary);
      color: var(--text-disabled);
    }
  }

  // Element Plus 组件暗色主题适配
  .el-card {
    background-color: var(--bg-primary);
    border-color: var(--border-color);

    .el-card__header {
      background-color: var(--bg-secondary);
      border-bottom-color: var(--border-color-light);
      color: var(--text-primary);
    }
  }

  .el-button {
    &--default {
      background-color: var(--bg-secondary);
      border-color: var(--border-color);
      color: var(--text-primary);

      &:hover {
        background-color: var(--bg-tertiary);
        border-color: var(--border-color-dark);
      }
    }
  }

  .el-input__wrapper {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);

    .el-input__inner {
      color: var(--text-primary);

      &::placeholder {
        color: var(--text-quaternary);
      }
    }
  }

  .el-select .el-input__wrapper {
    background-color: var(--bg-secondary);
  }

  .el-table {
    background-color: var(--bg-primary);
    color: var(--text-primary);

    .el-table__header {
      background-color: var(--bg-secondary);
    }

    .el-table__row {
      &:hover {
        background-color: var(--bg-secondary);
      }
    }

    th, td {
      border-bottom-color: var(--border-color);
    }
  }

  .el-dialog {
    background-color: var(--bg-primary);

    .el-dialog__header {
      background-color: var(--bg-secondary);
      border-bottom-color: var(--border-color-light);
    }
  }

  .el-menu {
    background-color: var(--bg-primary);

    .el-menu-item {
      color: var(--text-primary);

      &:hover {
        background-color: var(--bg-secondary);
      }

      &.is-active {
        background-color: var(--primary-600);
        color: var(--text-inverse);
      }
    }
  }

  // 滚动条暗色主题
  ::-webkit-scrollbar-track {
    background: var(--bg-secondary);
  }

  ::-webkit-scrollbar-thumb {
    background: var(--border-color);

    &:hover {
      background: var(--border-color-dark);
    }
  }

  ::-webkit-scrollbar-corner {
    background: var(--bg-secondary);
  }
}

// ==================== 主题切换工具类 ====================
.theme-auto {
  @media (prefers-color-scheme: dark) {
    // 重新定义颜色变量
    --bg-primary: var(--gray-900);
    --bg-secondary: var(--gray-800);
    --bg-tertiary: var(--gray-700);
    --bg-quaternary: var(--gray-600);

    --text-primary: var(--gray-50);
    --text-secondary: var(--gray-300);
    --text-tertiary: var(--gray-400);
    --text-quaternary: var(--gray-500);
    --text-disabled: var(--gray-600);

    --border-color: var(--gray-700);
    --border-color-light: var(--gray-600);
    --border-color-dark: var(--gray-500);

    // 调整阴影为暗色主题
    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
    --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
    --shadow-md: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
    --shadow-lg: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
    --shadow-xl: 0 25px 50px -12px rgb(0 0 0 / 0.5);

    // 状态背景色调整
    --bg-success: var(--success-900);
    --bg-warning: var(--warning-900);
    --bg-error: var(--error-900);
    --bg-info: var(--info-900);
  }
}

// ==================== 主题特定动画 ====================
.dark {
  .animate-fade-in {
    animation: darkFadeIn 0.3s ease-out;
  }
}

@keyframes darkFadeIn {
  from {
    opacity: 0;
    background-color: var(--bg-secondary);
  }
  to {
    opacity: 1;
    background-color: var(--bg-primary);
  }
}

// ==================== 主题切换按钮样式 ====================
.theme-toggle {
  position: relative;
  width: 48px;
  height: 24px;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-full);
  cursor: pointer;
  transition: var(--transition-all);

  &::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 18px;
    height: 18px;
    background-color: var(--bg-primary);
    border-radius: var(--border-radius-full);
    transition: var(--transition-all);
    box-shadow: var(--shadow-sm);
  }

  &.dark {
    background-color: var(--primary-600);

    &::before {
      transform: translateX(24px);
      background-color: var(--text-inverse);
    }
  }

  &:hover {
    box-shadow: var(--shadow-md);
  }
}

// ==================== 响应式主题适配 ====================
@media (max-width: 768px) {
  .dark {
    // 移动端暗色主题特殊处理
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.2);
    --shadow: 0 2px 4px 0 rgb(0 0 0 / 0.3);
  }
}

// ==================== 打印样式 ====================
@media print {
  .dark {
    // 打印时强制使用浅色主题
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --text-primary: #000000;
    --text-secondary: #333333;
    --border-color: #dee2e6;
  }
}
