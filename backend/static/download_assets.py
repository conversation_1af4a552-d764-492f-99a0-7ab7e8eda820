#!/usr/bin/env python3
"""
下载Swagger UI和ReDoc静态资源文件
"""
import os
import requests
from pathlib import Path

def download_file(url, filepath):
    """下载文件"""
    print(f"下载 {url} 到 {filepath}")
    response = requests.get(url)
    response.raise_for_status()
    
    with open(filepath, 'wb') as f:
        f.write(response.content)
    print(f"✅ 下载完成: {filepath}")

def main():
    # 创建static目录
    static_dir = Path(__file__).parent
    static_dir.mkdir(exist_ok=True)
    
    # Swagger UI 文件
    swagger_version = "4.15.5"
    swagger_files = {
        f"https://unpkg.com/swagger-ui-dist@{swagger_version}/swagger-ui-bundle.js": "swagger-ui-bundle.js",
        f"https://unpkg.com/swagger-ui-dist@{swagger_version}/swagger-ui.css": "swagger-ui.css",
    }
    
    # ReDoc 文件
    redoc_version = "2.0.0"
    redoc_files = {
        f"https://unpkg.com/redoc@{redoc_version}/bundles/redoc.standalone.js": "redoc.standalone.js",
    }
    
    # 下载所有文件
    all_files = {**swagger_files, **redoc_files}
    
    for url, filename in all_files.items():
        filepath = static_dir / filename
        try:
            download_file(url, filepath)
        except Exception as e:
            print(f"❌ 下载失败 {filename}: {e}")
    
    # 创建简单的favicon.ico
    favicon_path = static_dir / "favicon.ico"
    if not favicon_path.exists():
        # 创建一个简单的16x16像素的ico文件
        with open(favicon_path, 'wb') as f:
            # 简单的ICO文件头
            f.write(b'\x00\x00\x01\x00\x01\x00\x10\x10\x00\x00\x01\x00\x08\x00h\x05\x00\x00\x16\x00\x00\x00')
        print(f"✅ 创建favicon: {favicon_path}")

if __name__ == "__main__":
    main()