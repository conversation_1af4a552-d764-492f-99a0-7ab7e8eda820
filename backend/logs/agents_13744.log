2025-07-27 14:07:02 | INFO     | app.agents.factory:initialize:423 | 智能体工厂初始化中...
2025-07-27 14:07:02 | INFO     | app.agents.factory:initialize:425 | 智能体工厂初始化完成
2025-07-27 14:08:23 | INFO     | app.core.agents.collector:__init__:38 | test_case流式响应收集器初始化完成
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 文档解析智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 图片分析智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: API规范解析智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 数据库Schema解析智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 录屏分析智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 需求解析智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试点提取智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试用例生成智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: RAG知识库检索智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 思维导图生成智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: Excel导出智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试用例保存智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 需求存储智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 会话状态更新智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_stream_collector:414 | 流式响应收集器注册成功
2025-07-27 14:08:26 | INFO     | app.core.agents.base:__init__:47 | 初始化 图片分析智能体 智能体 (ID: image_analyzer)
2025-07-27 14:08:26 | INFO     | app.agents.test_case.image_analyzer_agent:__init__:59 | 图片分析智能体初始化完成: 图片分析智能体
2025-07-27 14:08:26 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 图片分析智能体
2025-07-27 14:08:26 | INFO     | app.agents.test_case.image_analyzer_agent:handle_image_analysis_request:69 | 开始处理图片分析请求: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:26 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: generic_analyzer (模型: qwenvl)
2025-07-27 14:08:26 | ERROR    | app.agents.test_case.image_analyzer_agent:_run_multimodal_analysis:645 | 多模态分析执行失败: Invalid message type in sequence: <class 'dict'>
2025-07-27 14:08:26 | INFO     | app.agents.test_case.image_analyzer_agent:_generate_test_cases_from_image:860 | 从图片生成了 1 个测试用例
2025-07-27 14:08:26 | INFO     | app.agents.test_case.image_analyzer_agent:_send_to_test_point_extractor:1023 | 已发送到测试点提取智能体: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:26 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试点提取智能体 智能体 (ID: test_point_extractor)
2025-07-27 14:08:26 | INFO     | app.agents.test_case.test_point_extraction_agent:__init__:91 | 测试点提取智能体初始化完成: 测试点提取智能体
2025-07-27 14:08:26 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试点提取智能体
2025-07-27 14:08:26 | INFO     | app.agents.test_case.test_point_extraction_agent:handle_test_point_extraction_request:103 | 开始处理测试点提取请求: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:27 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: test_point_extractor (模型: deepseek)
2025-07-27 14:09:41 | INFO     | app.agents.test_case.test_point_extraction_agent:_generate_test_cases_from_test_points:895 | 从测试点生成了 5 个测试用例
2025-07-27 14:09:41 | INFO     | app.agents.test_case.test_point_extraction_agent:_send_to_test_case_generator:944 | 已发送测试点提取响应到测试用例生成智能体: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:42 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试用例生成智能体 智能体 (ID: test_case_generator)
2025-07-27 14:09:42 | INFO     | app.agents.test_case.test_case_generator_agent:__init__:126 | 企业级测试用例生成智能体初始化完成: 测试用例生成智能体
2025-07-27 14:09:42 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试用例生成智能体
2025-07-27 14:09:42 | INFO     | app.agents.test_case.test_case_generator_agent:handle_test_point_extraction_response:144 | 开始处理测试点提取响应: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:42 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-27 14:10:16 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-27 14:10:48 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-27 14:11:21 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-27 14:11:54 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-27 14:12:24 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-27 14:12:59 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-27 14:13:34 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-27 14:14:14 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-27 14:14:54 | ERROR    | app.agents.test_case.test_case_generator_agent:_analyze_test_coverage:1101 | 分析测试覆盖度失败: 'NoneType' object has no attribute 'lower'
2025-07-27 14:14:54 | INFO     | app.agents.test_case.test_case_generator_agent:_generatetest_cases_from_test_points:371 | 成功生成了 9 个企业级测试用例
2025-07-27 14:14:54 | INFO     | app.agents.test_case.test_case_generator_agent:_sendsave_request:1292 | 已发送企业级保存请求到数据库智能体: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试用例保存智能体 智能体 (ID: test_case_saver)
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:__init__:108 | 测试用例保存智能体初始化完成: 测试用例保存智能体
2025-07-27 14:14:54 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试用例保存智能体
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:handle_test_case_save_request:130 | 开始处理测试用例保存请求: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | WARNING  | app.agents.database.test_case_saver_agent:_smart_match_requirements:1059 | 智能匹配需求失败: 'RequirementRepository' object has no attribute 'search_requirements_by_keywords'
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1031 | 测试用例 2aae544a-b732-414c-9baf-e3c7cf70dd2e 没有找到相关需求进行关联
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 图像分析失败处理测试 - 上传损坏图像文件
2025-07-27 14:14:54 | WARNING  | app.agents.database.test_case_saver_agent:_smart_match_requirements:1059 | 智能匹配需求失败: 'RequirementRepository' object has no attribute 'search_requirements_by_keywords'
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1031 | 测试用例 9ffeaf8b-abf1-4663-8b52-5a460ee32aa6 没有找到相关需求进行关联
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 图像分析失败处理测试 - 上传不支持的图像格式
2025-07-27 14:14:54 | WARNING  | app.agents.database.test_case_saver_agent:_smart_match_requirements:1059 | 智能匹配需求失败: 'RequirementRepository' object has no attribute 'search_requirements_by_keywords'
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1031 | 测试用例 80e99244-4019-488a-80bd-53f52ac017e7 没有找到相关需求进行关联
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 图像分析失败处理测试 - 服务超时场景模拟
2025-07-27 14:14:54 | WARNING  | app.agents.database.test_case_saver_agent:_smart_match_requirements:1059 | 智能匹配需求失败: 'RequirementRepository' object has no attribute 'search_requirements_by_keywords'
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1031 | 测试用例 f74c6b79-96d6-4154-9dc9-fe29b83084ab 没有找到相关需求进行关联
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 图像分析响应时间测试
2025-07-27 14:14:54 | WARNING  | app.agents.database.test_case_saver_agent:_smart_match_requirements:1059 | 智能匹配需求失败: 'RequirementRepository' object has no attribute 'search_requirements_by_keywords'
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1031 | 测试用例 e1b931d1-deb2-4154-a99f-88b5fa436eab 没有找到相关需求进行关联
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 图像上传安全测试
2025-07-27 14:14:54 | WARNING  | app.agents.database.test_case_saver_agent:_smart_match_requirements:1059 | 智能匹配需求失败: 'RequirementRepository' object has no attribute 'search_requirements_by_keywords'
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1031 | 测试用例 8fac7158-ca30-4536-8579-1f3435cffc1c 没有找到相关需求进行关联
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 图像服务与存储服务集成测试
2025-07-27 14:14:54 | WARNING  | app.agents.database.test_case_saver_agent:_smart_match_requirements:1059 | 智能匹配需求失败: 'RequirementRepository' object has no attribute 'search_requirements_by_keywords'
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1031 | 测试用例 5f71ca64-35f9-4ca9-95f0-37e5a7f7836c 没有找到相关需求进行关联
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 端到端图像分析验收测试
2025-07-27 14:14:54 | WARNING  | app.agents.database.test_case_saver_agent:_smart_match_requirements:1059 | 智能匹配需求失败: 'RequirementRepository' object has no attribute 'search_requirements_by_keywords'
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1031 | 测试用例 582259c7-14bf-49b5-a46f-64a4503f7166 没有找到相关需求进行关联
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 图像文件大小边界测试
2025-07-27 14:14:54 | WARNING  | app.agents.database.test_case_saver_agent:_smart_match_requirements:1059 | 智能匹配需求失败: 'RequirementRepository' object has no attribute 'search_requirements_by_keywords'
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1031 | 测试用例 6592322b-cbb3-491c-bb17-b7f16df6c721 没有找到相关需求进行关联
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 图像分析异常处理测试
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_cases_to_database:335 | 批量保存完成: 成功 9 个，失败 0 个
2025-07-27 14:14:54 | INFO     | app.agents.test_case.test_case_generator_agent:_sendmind_map_request:1341 | 已发送企业级思维导图生成请求: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | INFO     | app.core.agents.base:__init__:47 | 初始化 思维导图生成智能体 智能体 (ID: mind_map_generator)
2025-07-27 14:14:55 | INFO     | app.agents.test_case.mind_map_generator_agent:__init__:90 | 思维导图生成智能体初始化完成: 思维导图生成智能体
2025-07-27 14:14:55 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 思维导图生成智能体
2025-07-27 14:14:55 | INFO     | app.agents.test_case.mind_map_generator_agent:handle_mind_map_generation_request:113 | 开始处理思维导图生成请求: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | INFO     | app.agents.test_case.mind_map_generator_agent:_fetch_test_cases_data:255 | 获取到 9 个测试用例数据
2025-07-27 14:14:55 | INFO     | app.agents.test_case.mind_map_generator_agent:_generate_mind_map:304 | 思维导图生成完成: 14 节点, 13 边
2025-07-27 14:14:55 | INFO     | app.agents.test_case.mind_map_generator_agent:_save_mind_map_to_database:597 | 思维导图已保存到数据库: 53949922-e299-48df-a49f-c70e7f3330b8
2025-07-27 14:14:55 | WARNING  | app.agents.database.session_status_agent:update_session_status:486 | 运行时未初始化，直接调用数据库更新
2025-07-27 15:40:02 | INFO     | app.agents.factory:cleanup:433 | 清理智能体工厂资源...
2025-07-27 15:40:02 | INFO     | app.agents.factory:cleanup:435 | 智能体工厂资源清理完成
