2025-08-01 18:47:07 | ERROR    | app.agents.test_case.test_case_generator_agent:_parse_ai_json_response:667 | 第二次JSON解析也失败: Expecting ',' delimiter: line 66 column 39 (char 2053)
2025-08-01 18:47:07 | ERROR    | app.agents.test_case.test_case_generator_agent:_parse_ai_json_response:668 | 清理后的响应: {
    "preconditions": "1. 系统已部署并正常运行 2. 测试环境网络连接正常 3. 已注册有效的测试用户账号 4. 浏览器已清除缓存和cookies 5. 系统时间与标准时间同步",
    "test_steps": [
        {
            "step_number": 1,
            "action": "打开系统登录页面",
 ...
2025-08-01 18:47:07 | ERROR    | app.agents.test_case.test_case_generator_agent:_parse_ai_json_response:682 | JSON解析过程中发生错误: 无法解析AI响应为有效JSON: Expecting ',' delimiter: line 66 column 39 (char 2053)
2025-08-01 18:47:07 | ERROR    | app.agents.test_case.test_case_generator_agent:_ai_generate_detailed_test_case_content:625 | AI生成详细测试用例内容失败: 无法解析AI响应为有效JSON: Expecting ',' delimiter: line 66 column 39 (char 2053)
2025-08-01 18:47:07 | ERROR    | app.agents.test_case.test_case_generator_agent:_ai_generate_detailed_test_case_content:626 | 原始AI响应: ```json
{
    "preconditions": "1. 系统已部署并正常运行 2. 测试环境网络连接正常 3. 已注册有效的测试用户账号 4. 浏览器已清除缓存和cookies 5. 系统时间与标准时间同步",
    "test_steps": [
        {
            "step_number": 1,
            "action": "打开系统登录页面",
            "input_data": "系统URL",
            "expected_result": "成功加载登录页面，显示用户名和密码输入框",
            "notes": "验证页面元素加载完整"
        },
        {
            "step_number": 2,
            "action": "输入有效用户名",
            "input_data": "已注册的有效用户名",
            "expected_result": "用户名输入框显示输入内容",
2025-08-01 18:50:07 | ERROR    | app.agents.test_case.test_case_generator_agent:_analyze_test_coverage:1101 | 分析测试覆盖度失败: 'NoneType' object has no attribute 'lower'
2025-08-01 18:50:38 | ERROR    | app.api.v1.endpoints.test_case_generator:event_generator:1043 | 流式响应生成失败: 0ad5653e-9262-4f46-b746-b781ac18265a - '0ad5653e-9262-4f46-b746-b781ac18265a'
