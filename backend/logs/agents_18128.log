2025-08-01 17:23:04 | INFO     | app.agents.factory:initialize:423 | 智能体工厂初始化中...
2025-08-01 17:23:04 | INFO     | app.agents.factory:initialize:425 | 智能体工厂初始化完成
2025-08-01 17:24:42 | INFO     | app.core.agents.collector:__init__:38 | test_case流式响应收集器初始化完成
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 文档解析智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 图片分析智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: API规范解析智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 数据库Schema解析智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 录屏分析智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 需求解析智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试点提取智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试用例生成智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: RAG知识库检索智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 思维导图生成智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: Excel导出智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试用例保存智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 需求存储智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 会话状态更新智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_stream_collector:414 | 流式响应收集器注册成功
2025-08-01 17:24:44 | INFO     | app.core.agents.base:__init__:47 | 初始化 文档解析智能体 智能体 (ID: document_parser)
2025-08-01 17:24:44 | INFO     | app.agents.test_case.document_parser_agent:__init__:61 | 文档解析智能体初始化完成: 文档解析智能体
2025-08-01 17:24:44 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 文档解析智能体
2025-08-01 17:24:44 | INFO     | app.agents.test_case.document_parser_agent:handle_document_parse_request:73 | 开始处理文档解析请求: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:45 | INFO     | app.agents.test_case.document_parser_agent:_parse_pdf:227 | 开始解析PDF文档: uploads\documentsparser\20250801_172440_926c2068_0729b2e6-e770-49ea-a20e-256d420f4bea.pdf
2025-08-01 17:24:45 | INFO     | app.agents.test_case.document_parser_agent:_parse_pdf:285 | 第 1 页文本提取完成，内容长度: 617
2025-08-01 17:24:45 | INFO     | app.agents.test_case.document_parser_agent:_parse_pdf:285 | 第 2 页文本提取完成，内容长度: 433
2025-08-01 17:24:46 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: document_analyzer (模型: deepseek)
2025-08-01 17:24:48 | ERROR    | app.agents.test_case.document_parser_agent:_run_ai_analysis:980 | AI分析执行失败: Connection error.
2025-08-01 17:24:48 | INFO     | app.agents.test_case.document_parser_agent:_generate_test_cases_from_document:1065 | 从文档生成了 0 个测试用例
2025-08-01 17:24:48 | INFO     | app.agents.test_case.document_parser_agent:_send_to_test_point_extractor:1137 | 已发送到测试点提取智能体: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:49 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试点提取智能体 智能体 (ID: test_point_extractor)
2025-08-01 17:24:49 | INFO     | app.agents.test_case.test_point_extraction_agent:__init__:91 | 测试点提取智能体初始化完成: 测试点提取智能体
2025-08-01 17:24:49 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试点提取智能体
2025-08-01 17:24:49 | INFO     | app.agents.test_case.test_point_extraction_agent:handle_test_point_extraction_request:103 | 开始处理测试点提取请求: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:49 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: test_point_extractor (模型: deepseek)
2025-08-01 17:24:51 | ERROR    | app.agents.test_case.test_point_extraction_agent:_run_ai_extraction:704 | AI提取执行失败: Connection error.
2025-08-01 17:24:51 | INFO     | app.agents.test_case.test_point_extraction_agent:_generate_test_cases_from_test_points:895 | 从测试点生成了 0 个测试用例
2025-08-01 17:24:51 | INFO     | app.agents.test_case.test_point_extraction_agent:_send_to_test_case_generator:944 | 已发送测试点提取响应到测试用例生成智能体: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试用例生成智能体 智能体 (ID: test_case_generator)
2025-08-01 17:24:51 | INFO     | app.agents.test_case.test_case_generator_agent:__init__:126 | 企业级测试用例生成智能体初始化完成: 测试用例生成智能体
2025-08-01 17:24:51 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试用例生成智能体
2025-08-01 17:24:51 | INFO     | app.agents.test_case.test_case_generator_agent:handle_test_point_extraction_response:144 | 开始处理测试点提取响应: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | ERROR    | app.agents.test_case.test_case_generator_agent:_analyze_automation_feasibility:1145 | 分析自动化可行性失败: division by zero
2025-08-01 17:24:51 | INFO     | app.agents.test_case.test_case_generator_agent:_generatetest_cases_from_test_points:371 | 成功生成了 0 个企业级测试用例
2025-08-01 17:24:51 | WARNING  | app.agents.database.session_status_agent:update_session_status:486 | 运行时未初始化，直接调用数据库更新
2025-08-01 18:29:52 | INFO     | app.agents.factory:cleanup:433 | 清理智能体工厂资源...
2025-08-01 18:29:52 | INFO     | app.agents.factory:cleanup:435 | 智能体工厂资源清理完成
