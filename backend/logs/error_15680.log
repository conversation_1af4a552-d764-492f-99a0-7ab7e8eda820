2025-07-29 17:58:46 | ERROR    | app.api.v1.endpoints.tags:create_tag:143 | 创建标签失败: (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`test_case_automation`.`tags`, CONSTRAINT `tags_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE)')
[SQL: INSERT INTO tags (id, name, color, project_id, usage_count, created_at) VALUES (%s, %s, %s, %s, %s, %s)]
[parameters: ('519c07a2-983d-4609-a945-f33fb5cd72c7', 'test', '#2563eb', '', 0, datetime.datetime(2025, 7, 29, 9, 58, 46, 685160))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
