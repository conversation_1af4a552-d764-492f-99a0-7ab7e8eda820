2025-08-01 18:35:23 | INFO     | app.agents.factory:initialize:423 | 智能体工厂初始化中...
2025-08-01 18:35:23 | INFO     | app.agents.factory:initialize:425 | 智能体工厂初始化完成
2025-08-01 18:35:52 | INFO     | app.core.agents.collector:__init__:38 | test_case流式响应收集器初始化完成
2025-08-01 18:35:52 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 文档解析智能体
2025-08-01 18:35:52 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 图片分析智能体
2025-08-01 18:35:52 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: API规范解析智能体
2025-08-01 18:35:52 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 数据库Schema解析智能体
2025-08-01 18:35:52 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 录屏分析智能体
2025-08-01 18:35:52 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 需求解析智能体
2025-08-01 18:35:52 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试点提取智能体
2025-08-01 18:35:52 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试用例生成智能体
2025-08-01 18:35:52 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: RAG知识库检索智能体
2025-08-01 18:35:52 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 思维导图生成智能体
2025-08-01 18:35:52 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: Excel导出智能体
2025-08-01 18:35:52 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试用例保存智能体
2025-08-01 18:35:52 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 需求存储智能体
2025-08-01 18:35:52 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 会话状态更新智能体
2025-08-01 18:35:52 | INFO     | app.agents.factory:register_stream_collector:414 | 流式响应收集器注册成功
2025-08-01 18:35:54 | INFO     | app.core.agents.base:__init__:47 | 初始化 文档解析智能体 智能体 (ID: document_parser)
2025-08-01 18:35:54 | INFO     | app.agents.test_case.document_parser_agent:__init__:61 | 文档解析智能体初始化完成: 文档解析智能体
2025-08-01 18:35:54 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 文档解析智能体
2025-08-01 18:35:54 | INFO     | app.agents.test_case.document_parser_agent:handle_document_parse_request:73 | 开始处理文档解析请求: 0ad5653e-9262-4f46-b746-b781ac18265a
2025-08-01 18:35:54 | INFO     | app.agents.test_case.document_parser_agent:_parse_pdf:227 | 开始解析PDF文档: uploads\documentsparser\20250801_183550_0ad5653e_0729b2e6-e770-49ea-a20e-256d420f4bea.pdf
2025-08-01 18:35:54 | INFO     | app.agents.test_case.document_parser_agent:_parse_pdf:285 | 第 1 页文本提取完成，内容长度: 617
2025-08-01 18:35:54 | INFO     | app.agents.test_case.document_parser_agent:_parse_pdf:285 | 第 2 页文本提取完成，内容长度: 433
2025-08-01 18:35:55 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: document_analyzer (模型: deepseek)
2025-08-01 18:36:42 | INFO     | app.agents.test_case.document_parser_agent:_generate_test_cases_from_document:1065 | 从文档生成了 8 个测试用例
2025-08-01 18:36:42 | INFO     | app.agents.test_case.document_parser_agent:_save_requirements_to_database:1177 | 已发送需求保存请求到需求存储智能体: 0ad5653e-9262-4f46-b746-b781ac18265a
2025-08-01 18:36:42 | INFO     | app.agents.test_case.document_parser_agent:_send_to_test_point_extractor:1137 | 已发送到测试点提取智能体: 0ad5653e-9262-4f46-b746-b781ac18265a
2025-08-01 18:36:43 | INFO     | app.core.agents.base:__init__:47 | 初始化 需求存储智能体 智能体 (ID: requirement_saver)
2025-08-01 18:36:43 | INFO     | app.agents.database.requirement_saver_agent:__init__:78 | 需求存储智能体初始化完成: 需求存储智能体
2025-08-01 18:36:43 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 需求存储智能体
2025-08-01 18:36:43 | INFO     | app.agents.database.requirement_saver_agent:handle_requirement_save_request:91 | 开始处理需求保存请求: 0ad5653e-9262-4f46-b746-b781ac18265a
2025-08-01 18:36:43 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试点提取智能体 智能体 (ID: test_point_extractor)
2025-08-01 18:36:43 | INFO     | app.agents.test_case.test_point_extraction_agent:__init__:91 | 测试点提取智能体初始化完成: 测试点提取智能体
2025-08-01 18:36:43 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试点提取智能体
2025-08-01 18:36:43 | INFO     | app.agents.test_case.test_point_extraction_agent:handle_test_point_extraction_request:103 | 开始处理测试点提取请求: 0ad5653e-9262-4f46-b746-b781ac18265a
2025-08-01 18:36:43 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: test_point_extractor (模型: deepseek)
2025-08-01 18:38:54 | INFO     | app.agents.test_case.test_point_extraction_agent:_generate_test_cases_from_test_points:895 | 从测试点生成了 6 个测试用例
2025-08-01 18:38:54 | INFO     | app.agents.test_case.test_point_extraction_agent:_send_to_test_case_generator:944 | 已发送测试点提取响应到测试用例生成智能体: 0ad5653e-9262-4f46-b746-b781ac18265a
2025-08-01 18:38:54 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试用例生成智能体 智能体 (ID: test_case_generator)
2025-08-01 18:38:54 | INFO     | app.agents.test_case.test_case_generator_agent:__init__:126 | 企业级测试用例生成智能体初始化完成: 测试用例生成智能体
2025-08-01 18:38:54 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试用例生成智能体
2025-08-01 18:38:54 | INFO     | app.agents.test_case.test_case_generator_agent:handle_test_point_extraction_response:144 | 开始处理测试点提取响应: 0ad5653e-9262-4f46-b746-b781ac18265a
2025-08-01 18:38:55 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-08-01 18:39:42 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-08-01 18:40:18 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-08-01 18:40:51 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-08-01 18:41:28 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-08-01 18:41:59 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-08-01 18:42:50 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-08-01 18:43:24 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-08-01 18:43:54 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-08-01 18:44:38 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-08-01 18:45:24 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-08-01 18:46:18 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-08-01 18:47:07 | WARNING  | app.agents.test_case.test_case_generator_agent:_parse_ai_json_response:648 | 第一次JSON解析失败: Expecting ',' delimiter: line 66 column 39 (char 2053)
2025-08-01 18:47:07 | ERROR    | app.agents.test_case.test_case_generator_agent:_parse_ai_json_response:667 | 第二次JSON解析也失败: Expecting ',' delimiter: line 66 column 39 (char 2053)
2025-08-01 18:47:07 | ERROR    | app.agents.test_case.test_case_generator_agent:_parse_ai_json_response:668 | 清理后的响应: {
    "preconditions": "1. 系统已部署并正常运行 2. 测试环境网络连接正常 3. 已注册有效的测试用户账号 4. 浏览器已清除缓存和cookies 5. 系统时间与标准时间同步",
    "test_steps": [
        {
            "step_number": 1,
            "action": "打开系统登录页面",
 ...
2025-08-01 18:47:07 | ERROR    | app.agents.test_case.test_case_generator_agent:_parse_ai_json_response:682 | JSON解析过程中发生错误: 无法解析AI响应为有效JSON: Expecting ',' delimiter: line 66 column 39 (char 2053)
2025-08-01 18:47:07 | ERROR    | app.agents.test_case.test_case_generator_agent:_ai_generate_detailed_test_case_content:625 | AI生成详细测试用例内容失败: 无法解析AI响应为有效JSON: Expecting ',' delimiter: line 66 column 39 (char 2053)
2025-08-01 18:47:07 | ERROR    | app.agents.test_case.test_case_generator_agent:_ai_generate_detailed_test_case_content:626 | 原始AI响应: ```json
{
    "preconditions": "1. 系统已部署并正常运行 2. 测试环境网络连接正常 3. 已注册有效的测试用户账号 4. 浏览器已清除缓存和cookies 5. 系统时间与标准时间同步",
    "test_steps": [
        {
            "step_number": 1,
            "action": "打开系统登录页面",
            "input_data": "系统URL",
            "expected_result": "成功加载登录页面，显示用户名和密码输入框",
            "notes": "验证页面元素加载完整"
        },
        {
            "step_number": 2,
            "action": "输入有效用户名",
            "input_data": "已注册的有效用户名",
            "expected_result": "用户名输入框显示输入内容",
2025-08-01 18:47:08 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-08-01 18:47:43 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-08-01 18:48:16 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-08-01 18:48:54 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-08-01 18:49:27 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-08-01 18:50:07 | ERROR    | app.agents.test_case.test_case_generator_agent:_analyze_test_coverage:1101 | 分析测试覆盖度失败: 'NoneType' object has no attribute 'lower'
2025-08-01 18:50:07 | INFO     | app.agents.test_case.test_case_generator_agent:_generatetest_cases_from_test_points:371 | 成功生成了 17 个企业级测试用例
2025-08-01 18:50:07 | INFO     | app.agents.test_case.test_case_generator_agent:_sendsave_request:1292 | 已发送企业级保存请求到数据库智能体: 0ad5653e-9262-4f46-b746-b781ac18265a
2025-08-01 18:50:07 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试用例保存智能体 智能体 (ID: test_case_saver)
2025-08-01 18:50:07 | INFO     | app.agents.database.test_case_saver_agent:__init__:108 | 测试用例保存智能体初始化完成: 测试用例保存智能体
2025-08-01 18:50:07 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试用例保存智能体
2025-08-01 18:50:07 | INFO     | app.agents.database.test_case_saver_agent:handle_test_case_save_request:130 | 开始处理测试用例保存请求: 0ad5653e-9262-4f46-b746-b781ac18265a
2025-08-01 18:50:07 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 8d838064-752e-4fe7-ab0a-625e29dbc81a
2025-08-01 18:50:07 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 正常登录流程验证 - 有效邮箱和密码组合登录
2025-08-01 18:50:07 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 afad1559-df6b-40c2-b4a2-94cb0ac7f683
2025-08-01 18:50:07 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 正常登录流程验证 - 已激活账户登录
2025-08-01 18:50:07 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 69597ec0-734c-4b5d-bde8-7e7dcb843c1d
2025-08-01 18:50:07 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 正常登录流程验证 - 首次登录成功获取access-token
2025-08-01 18:50:07 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 0e91f059-7219-421f-9833-2fdfe74aa8b6
2025-08-01 18:50:07 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 认证失败场景测试 - 错误密码登录(1100002)
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 fb2a9ffa-7be9-472f-ab08-852487003d76
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 认证失败场景测试 - 不存在的邮箱登录
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 518b2f93-51ff-41a9-9298-c6b4a1d0372d
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 认证失败场景测试 - 已禁用账户登录
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 e65ece6d-f139-40db-988f-68eb32f77788
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 认证失败场景测试 - 空密码登录
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 93f92625-a0c4-421c-a3a0-10a24c927d96
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 认证失败场景测试 - 特殊字符密码登录
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 76542a9d-6d6d-4239-ac90-c0f9ceed0e31
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 登录API性能基准测试
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 2cb9d99e-57dd-498a-be3a-bb2ed46dfdfe
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: API安全防护测试
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 836caaac-067e-432a-b0b3-9cc5d4ae381e
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 认证服务与用户服务集成
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 7c9882d7-2813-48b5-b5c8-f27cf63d0050
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 终端用户登录体验测试
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 494069ec-73f0-4486-ad97-c3d10ba7deb0
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 输入字段边界测试
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 99e20b87-5275-4d95-b80f-09530a38ca5b
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 异常请求处理测试 - 缺失password参数
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 40aa4170-d84f-4a71-9105-24ca1b0382aa
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 异常请求处理测试 - email参数传入非字符串类型
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 e2a37e58-8353-4217-ac7f-36811219c55c
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 异常请求处理测试 - 10MB大请求体
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 96d8a16e-2309-4586-ab6d-6b90a8086a43
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 异常请求处理测试 - 5秒内重复相同请求
2025-08-01 18:50:08 | INFO     | app.agents.database.test_case_saver_agent:_save_test_cases_to_database:335 | 批量保存完成: 成功 17 个，失败 0 个
2025-08-01 18:50:08 | INFO     | app.agents.test_case.test_case_generator_agent:_sendmind_map_request:1341 | 已发送企业级思维导图生成请求: 0ad5653e-9262-4f46-b746-b781ac18265a
2025-08-01 18:50:08 | INFO     | app.core.agents.base:__init__:47 | 初始化 思维导图生成智能体 智能体 (ID: mind_map_generator)
2025-08-01 18:50:08 | INFO     | app.agents.test_case.mind_map_generator_agent:__init__:90 | 思维导图生成智能体初始化完成: 思维导图生成智能体
2025-08-01 18:50:08 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 思维导图生成智能体
2025-08-01 18:50:08 | INFO     | app.agents.test_case.mind_map_generator_agent:handle_mind_map_generation_request:113 | 开始处理思维导图生成请求: 0ad5653e-9262-4f46-b746-b781ac18265a
2025-08-01 18:50:08 | INFO     | app.agents.test_case.mind_map_generator_agent:_fetch_test_cases_data:255 | 获取到 17 个测试用例数据
2025-08-01 18:50:08 | INFO     | app.agents.test_case.mind_map_generator_agent:_generate_mind_map:304 | 思维导图生成完成: 22 节点, 21 边
2025-08-01 18:50:08 | INFO     | app.agents.test_case.mind_map_generator_agent:_save_mind_map_to_database:597 | 思维导图已保存到数据库: f8702715-5819-488b-b072-a33d3f92c789
2025-08-01 18:50:08 | WARNING  | app.agents.database.session_status_agent:update_session_status:486 | 运行时未初始化，直接调用数据库更新
