2025-07-27 14:07:01 | INFO     | app.core.logging:setup_logging:135 | 日志系统初始化完成
2025-07-27 14:07:02 | INFO     | main:lifespan:41 | 🚀 启动企业级测试用例生成系统...
2025-07-27 14:07:02 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: localhost:3306/test_case_automation
2025-07-27 14:07:02 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-07-27 14:07:02 | INFO     | main:lifespan:46 | ✅ 数据库连接初始化完成
2025-07-27 14:07:02 | INFO     | main:lifespan:64 | ✅ 上传目录创建完成
2025-07-27 14:07:02 | INFO     | app.agents.factory:initialize:423 | 智能体工厂初始化中...
2025-07-27 14:07:02 | INFO     | app.agents.factory:initialize:425 | 智能体工厂初始化完成
2025-07-27 14:07:02 | INFO     | main:lifespan:69 | ✅ 智能体工厂初始化完成
2025-07-27 14:07:02 | INFO     | main:lifespan:71 | 🎉 系统启动完成!
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 16 个历史会话
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 d818be96-fb44-4912-a1da-0922cbf4b028: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 a0d50609-59bd-44a4-aabd-9c1ffb2bcb26: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 5be0dc46-e3ae-45f8-b286-441259f20e80: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 bb00e953-0d48-4546-be96-a3ae223c7908: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 b5649e9e-375b-4001-89ab-5ce7d94d7dc8: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 9146b633-68d0-476c-b2ee-867859a3cf28: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 4e21c355-d581-4f13-a7ea-5919a25548c7: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 525fcf4e-85be-4cf9-971c-648bdadc75f6: status=processing, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 f168c0c6-401c-4fc8-b70f-80d482019260: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 47b93076-15a5-4a50-beb0-f5002e976ed3: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 817eeef3-bbea-43ac-97dc-10b4d5520abb: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ea46ee94-ad6f-4e6a-9634-d41a5df2720d: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 2d184a69-9a48-4c79-aea4-6a2d680ed278: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 dda3ccaa-a362-4f66-93b5-6c690157584f: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 0768af9c-7098-4575-b989-f3e925ec1485: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ef11c0ce-7eaf-4fda-b0f1-73d934203667: status=completed, progress=10.00
2025-07-27 14:08:20 | INFO     | app.utils.session_db_utils:create_processing_session:49 | 创建处理会话记录成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:20 | INFO     | app.api.v1.endpoints.test_case_generator:create_generation_session:301 | 创建测试用例生成会话: 89a7126e-885d-4a92-b00a-df74e58ea4dc, 类型: file
2025-07-27 14:08:21 | DEBUG    | app.api.v1.endpoints.test_case_generator:upload_file:478 | 开始更新数据库状态: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:21 | INFO     | app.utils.session_db_utils:update_session_status:129 | 更新会话状态成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc -> processing
2025-07-27 14:08:21 | DEBUG    | app.utils.session_db_utils:update_session_progress:170 | 更新会话进度成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc -> 10.0%
2025-07-27 14:08:21 | DEBUG    | app.api.v1.endpoints.test_case_generator:upload_file:496 | 数据库状态更新成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:21 | INFO     | app.api.v1.endpoints.test_case_generator:upload_file:504 | 文件上传成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc, 文件: 4dcf934e460743b0ab9ab2c2113386fd.png, 智能体: image_analyzer
2025-07-27 14:08:21 | INFO     | app.utils.session_db_utils:update_session_status:129 | 更新会话状态成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc -> processing
2025-07-27 14:08:21 | INFO     | app.api.v1.endpoints.test_case_generator:process_file:829 | 文件处理开始，会话 89a7126e-885d-4a92-b00a-df74e58ea4dc 状态已更新为 processing
2025-07-27 14:08:23 | INFO     | app.core.agents.collector:__init__:38 | test_case流式响应收集器初始化完成
2025-07-27 14:08:23 | DEBUG    | app.core.agents.collector:set_callback:47 | 设置流式响应回调函数
2025-07-27 14:08:23 | INFO     | app.services.test_case.orchestrator_service:__init__:66 | 测试用例智能体编排器初始化完成
2025-07-27 14:08:23 | INFO     | app.services.test_case.orchestrator_service:analyze_image:363 | 开始图片分析工作流: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:23 | INFO     | app.services.test_case.orchestrator_service:initialize:76 | 🚀 初始化测试用例智能体编排器...
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 文档解析智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 图片分析智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: API规范解析智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 数据库Schema解析智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 录屏分析智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 需求解析智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试点提取智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试用例生成智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: RAG知识库检索智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 思维导图生成智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: Excel导出智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试用例保存智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 需求存储智能体
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 会话状态更新智能体
2025-07-27 14:08:23 | INFO     | app.services.test_case.orchestrator_service:_register_test_case_agents:261 | 所有测试用例智能体注册完成
2025-07-27 14:08:23 | INFO     | app.agents.factory:register_stream_collector:414 | 流式响应收集器注册成功
2025-07-27 14:08:23 | INFO     | app.services.test_case.orchestrator_service:initialize:92 | ✅ 测试用例智能体编排器初始化完成
2025-07-27 14:08:23 | INFO     | app.services.test_case.orchestrator_service:_initialize_runtime:116 | 会话已记录: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:23 | INFO     | app.services.test_case.orchestrator_service:analyze_image:374 | 图片分析工作流启动完成: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:26 | INFO     | app.core.llms:get_qwenvl_model_client:51 | QwenVL模型客户端创建成功
2025-07-27 14:08:26 | INFO     | app.core.agents.base:__init__:47 | 初始化 图片分析智能体 智能体 (ID: image_analyzer)
2025-07-27 14:08:26 | INFO     | app.agents.test_case.image_analyzer_agent:__init__:59 | 图片分析智能体初始化完成: 图片分析智能体
2025-07-27 14:08:26 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 图片分析智能体
2025-07-27 14:08:26 | INFO     | app.agents.test_case.image_analyzer_agent:handle_image_analysis_request:69 | 开始处理图片分析请求: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:26 | DEBUG    | app.core.agents.base:_send_message:88 | [图片分析智能体] 发送message: 🔍 开始分析图片: 4dcf934e460743b0ab9ab2c2113386fd.png...
2025-07-27 14:08:26 | DEBUG    | app.core.agents.base:_send_message:88 | [图片分析智能体] 发送message: 📊 检测到图片类型: png...
2025-07-27 14:08:26 | DEBUG    | app.core.agents.base:_send_message:88 | [图片分析智能体] 发送message: 🔍 正在进行通用图片分析......
2025-07-27 14:08:26 | INFO     | app.core.llms:get_qwenvl_model_client:51 | QwenVL模型客户端创建成功
2025-07-27 14:08:26 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: generic_analyzer (模型: qwenvl)
2025-07-27 14:08:26 | ERROR    | app.agents.test_case.image_analyzer_agent:_run_multimodal_analysis:645 | 多模态分析执行失败: Invalid message type in sequence: <class 'dict'>
2025-07-27 14:08:26 | INFO     | app.agents.test_case.image_analyzer_agent:_generate_test_cases_from_image:860 | 从图片生成了 1 个测试用例
2025-07-27 14:08:26 | DEBUG    | app.core.agents.base:_send_message:88 | [图片分析智能体] 发送message: ✅ 图片分析完成，已生成测试用例...
2025-07-27 14:08:26 | INFO     | app.agents.test_case.image_analyzer_agent:_send_to_test_point_extractor:1023 | 已发送到测试点提取智能体: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:26 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 开始分析图片: 4dcf934e460743b0ab9ab2c2113386fd.png
2025-07-27 14:08:26 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:26 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 检测到图片类型: png
2025-07-27 14:08:26 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:26 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 正在进行通用图片分析...
2025-07-27 14:08:26 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:26 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 图片分析完成，已生成测试用例
2025-07-27 14:08:26 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:26 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-27 14:08:26 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试点提取智能体 智能体 (ID: test_point_extractor)
2025-07-27 14:08:26 | INFO     | app.agents.test_case.test_point_extraction_agent:__init__:91 | 测试点提取智能体初始化完成: 测试点提取智能体
2025-07-27 14:08:26 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试点提取智能体
2025-07-27 14:08:26 | INFO     | app.agents.test_case.test_point_extraction_agent:handle_test_point_extraction_request:103 | 开始处理测试点提取请求: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:26 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: 🎯 开始企业级测试点提取: 基于需求解析结果...
2025-07-27 14:08:26 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: 📊 需求分析输入: 1 个需求, 0 个业务流程...
2025-07-27 14:08:26 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: 🔄 第1步: 开始专业测试点提取分析......
2025-07-27 14:08:27 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-27 14:08:27 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: test_point_extractor (模型: deepseek)
2025-07-27 14:08:27 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🎯 开始企业级测试点提取: 基于需求解析结果
2025-07-27 14:08:27 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:27 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 需求分析输入: 1 个需求, 0 个业务流程
2025-07-27 14:08:27 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:27 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第1步: 开始专业测试点提取分析...
2025-07-27 14:08:27 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:27 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 图片分析智能体 - info
2025-07-27 14:08:27 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 图片分析智能体
2025-07-27 14:08:27 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 图片分析智能体 - completion
2025-07-27 14:08:27 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 图片分析智能体
2025-07-27 14:08:27 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试点提取智能体 - info
2025-07-27 14:08:27 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试点提取智能体 - info
2025-07-27 14:08:27 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试点提取智能体
2025-07-27 14:08:27 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试点提取智能体
2025-07-27 14:08:27 | DEBUG    | app.utils.agent_message_log_utils:update_session_logs_summary:181 | 更新会话日志摘要成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:27 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:131 | 会话日志摘要已更新: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:41 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: 📈 测试点提取完成: 功能测试点 1 个, 非功能测试点 2 个, 集成测试点 1 个, 总计 7 ...
2025-07-27 14:09:41 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: 🔄 第2步: 基于测试点生成测试用例......
2025-07-27 14:09:41 | INFO     | app.agents.test_case.test_point_extraction_agent:_generate_test_cases_from_test_points:895 | 从测试点生成了 5 个测试用例
2025-07-27 14:09:41 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: ✅ 成功生成 5 个测试用例...
2025-07-27 14:09:41 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: ✅ 测试点提取完成! 处理时间: 75.08秒...
2025-07-27 14:09:41 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: 🔄 转发到测试用例生成智能体进行用例生成......
2025-07-27 14:09:41 | INFO     | app.agents.test_case.test_point_extraction_agent:_send_to_test_case_generator:944 | 已发送测试点提取响应到测试用例生成智能体: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📈 测试点提取完成: 功能测试点 1 个, 非功能测试点 2 个, 集成测试点 1 个, 总计 7 个测试点
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第2步: 基于测试点生成测试用例...
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 成功生成 5 个测试用例
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 测试点提取完成! 处理时间: 75.08秒
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 转发到测试用例生成智能体进行用例生成...
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:42 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-27 14:09:42 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试用例生成智能体 智能体 (ID: test_case_generator)
2025-07-27 14:09:42 | INFO     | app.agents.test_case.test_case_generator_agent:__init__:126 | 企业级测试用例生成智能体初始化完成: 测试用例生成智能体
2025-07-27 14:09:42 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试用例生成智能体
2025-07-27 14:09:42 | INFO     | app.agents.test_case.test_case_generator_agent:handle_test_point_extraction_response:144 | 开始处理测试点提取响应: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:42 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🏭 开始企业级测试用例生成，基于专业测试点提取结果...
2025-07-27 14:09:42 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 📊 测试点分析: 功能测试点 1 个, 非功能测试点 2 个, 集成测试点 1 个, 总计 7 个测...
2025-07-27 14:09:42 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🔄 第1步: 基于测试点生成企业级测试用例......
2025-07-27 14:09:42 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🏭 正在基于专业测试点和RAG上下文生成企业级测试用例......
2025-07-27 14:09:42 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 📝 处理 1 个功能测试点......
2025-07-27 14:09:42 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-27 14:09:42 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🏭 开始企业级测试用例生成，基于专业测试点提取结果
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 测试点分析: 功能测试点 1 个, 非功能测试点 2 个, 集成测试点 1 个, 总计 7 个测试点
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第1步: 基于测试点生成企业级测试用例...
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:42 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试点提取智能体 - info
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🏭 正在基于专业测试点和RAG上下文生成企业级测试用例...
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📝 处理 1 个功能测试点...
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:42 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试点提取智能体
2025-07-27 14:09:42 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试点提取智能体 - success
2025-07-27 14:09:42 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试点提取智能体 - info
2025-07-27 14:09:42 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试点提取智能体 - success
2025-07-27 14:09:42 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试点提取智能体 - success
2025-07-27 14:09:42 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试点提取智能体
2025-07-27 14:09:42 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试点提取智能体
2025-07-27 14:09:42 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试点提取智能体
2025-07-27 14:09:42 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试点提取智能体
2025-07-27 14:09:42 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体 - progress
2025-07-27 14:09:42 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体
2025-07-27 14:09:42 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体 - info
2025-07-27 14:09:42 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体
2025-07-27 14:09:42 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体 - info
2025-07-27 14:09:42 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体 - metrics
2025-07-27 14:09:42 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体
2025-07-27 14:09:42 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体
2025-07-27 14:10:16 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-27 14:10:16 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-27 14:10:48 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-27 14:10:48 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-27 14:11:20 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: ⚡ 处理 2 个非功能测试点......
2025-07-27 14:11:21 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-27 14:11:21 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-27 14:11:21 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ⚡ 处理 2 个非功能测试点...
2025-07-27 14:11:21 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:11:54 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-27 14:11:54 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-27 14:12:24 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🔗 处理 1 个集成测试点......
2025-07-27 14:12:24 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-27 14:12:24 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-27 14:12:24 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 处理 1 个集成测试点...
2025-07-27 14:12:24 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:12:59 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: ✅ 处理 1 个验收测试点......
2025-07-27 14:12:59 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-27 14:12:59 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-27 14:12:59 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 处理 1 个验收测试点...
2025-07-27 14:12:59 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:13:34 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🎯 处理 1 个边界测试点......
2025-07-27 14:13:34 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-27 14:13:34 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-27 14:13:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🎯 处理 1 个边界测试点...
2025-07-27 14:13:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:13 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🚨 处理 1 个异常测试点......
2025-07-27 14:14:14 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-27 14:14:14 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-27 14:14:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🚨 处理 1 个异常测试点...
2025-07-27 14:14:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | ERROR    | app.agents.test_case.test_case_generator_agent:_analyze_test_coverage:1101 | 分析测试覆盖度失败: 'NoneType' object has no attribute 'lower'
2025-07-27 14:14:54 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 📊 企业级测试用例生成完成: 总计 9 个测试用例, 质量评分: 0.80...
2025-07-27 14:14:54 | INFO     | app.agents.test_case.test_case_generator_agent:_generatetest_cases_from_test_points:371 | 成功生成了 9 个企业级测试用例
2025-07-27 14:14:54 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: ✅ 企业级测试用例生成完成: 共生成 9 个测试用例...
2025-07-27 14:14:54 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🔄 第2步: 保存企业级测试用例到数据库......
2025-07-27 14:14:54 | INFO     | app.agents.test_case.test_case_generator_agent:_sendsave_request:1292 | 已发送企业级保存请求到数据库智能体: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 企业级测试用例生成完成: 总计 9 个测试用例, 质量评分: 0.80
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 企业级测试用例生成完成: 共生成 9 个测试用例
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第2步: 保存企业级测试用例到数据库...
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-27 14:14:54 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试用例保存智能体 智能体 (ID: test_case_saver)
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:__init__:108 | 测试用例保存智能体初始化完成: 测试用例保存智能体
2025-07-27 14:14:54 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试用例保存智能体
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:handle_test_case_save_request:130 | 开始处理测试用例保存请求: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 💾 开始保存 9 个测试用例到数据库......
2025-07-27 14:14:54 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 🔍 正在进行批量数据验证......
2025-07-27 14:14:54 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 📦 正在处理第 1/1 批数据 (9 个测试用例)......
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 💾 开始保存 9 个测试用例到数据库...
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 正在进行批量数据验证...
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📦 正在处理第 1/1 批数据 (9 个测试用例)...
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体 - success
2025-07-27 14:14:54 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体 - success
2025-07-27 14:14:54 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体 - info
2025-07-27 14:14:54 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体
2025-07-27 14:14:54 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体
2025-07-27 14:14:54 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体
2025-07-27 14:14:54 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例保存智能体 - info
2025-07-27 14:14:54 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例保存智能体
2025-07-27 14:14:54 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: 2aae544a-b732-414c-9baf-e3c7cf70dd2e
2025-07-27 14:14:54 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 89a7126e-885d-4a92-b00a-df74e58ea4dc 查询到 0 个需求
2025-07-27 14:14:54 | WARNING  | app.agents.database.test_case_saver_agent:_smart_match_requirements:1059 | 智能匹配需求失败: 'RequirementRepository' object has no attribute 'search_requirements_by_keywords'
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1031 | 测试用例 2aae544a-b732-414c-9baf-e3c7cf70dd2e 没有找到相关需求进行关联
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 图像分析失败处理测试 - 上传损坏图像文件
2025-07-27 14:14:54 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: 9ffeaf8b-abf1-4663-8b52-5a460ee32aa6
2025-07-27 14:14:54 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 89a7126e-885d-4a92-b00a-df74e58ea4dc 查询到 0 个需求
2025-07-27 14:14:54 | WARNING  | app.agents.database.test_case_saver_agent:_smart_match_requirements:1059 | 智能匹配需求失败: 'RequirementRepository' object has no attribute 'search_requirements_by_keywords'
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1031 | 测试用例 9ffeaf8b-abf1-4663-8b52-5a460ee32aa6 没有找到相关需求进行关联
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 图像分析失败处理测试 - 上传不支持的图像格式
2025-07-27 14:14:54 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: 80e99244-4019-488a-80bd-53f52ac017e7
2025-07-27 14:14:54 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 89a7126e-885d-4a92-b00a-df74e58ea4dc 查询到 0 个需求
2025-07-27 14:14:54 | WARNING  | app.agents.database.test_case_saver_agent:_smart_match_requirements:1059 | 智能匹配需求失败: 'RequirementRepository' object has no attribute 'search_requirements_by_keywords'
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1031 | 测试用例 80e99244-4019-488a-80bd-53f52ac017e7 没有找到相关需求进行关联
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 图像分析失败处理测试 - 服务超时场景模拟
2025-07-27 14:14:54 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: f74c6b79-96d6-4154-9dc9-fe29b83084ab
2025-07-27 14:14:54 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 89a7126e-885d-4a92-b00a-df74e58ea4dc 查询到 0 个需求
2025-07-27 14:14:54 | WARNING  | app.agents.database.test_case_saver_agent:_smart_match_requirements:1059 | 智能匹配需求失败: 'RequirementRepository' object has no attribute 'search_requirements_by_keywords'
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1031 | 测试用例 f74c6b79-96d6-4154-9dc9-fe29b83084ab 没有找到相关需求进行关联
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 图像分析响应时间测试
2025-07-27 14:14:54 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: e1b931d1-deb2-4154-a99f-88b5fa436eab
2025-07-27 14:14:54 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 89a7126e-885d-4a92-b00a-df74e58ea4dc 查询到 0 个需求
2025-07-27 14:14:54 | WARNING  | app.agents.database.test_case_saver_agent:_smart_match_requirements:1059 | 智能匹配需求失败: 'RequirementRepository' object has no attribute 'search_requirements_by_keywords'
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1031 | 测试用例 e1b931d1-deb2-4154-a99f-88b5fa436eab 没有找到相关需求进行关联
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 图像上传安全测试
2025-07-27 14:14:54 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: 8fac7158-ca30-4536-8579-1f3435cffc1c
2025-07-27 14:14:54 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 89a7126e-885d-4a92-b00a-df74e58ea4dc 查询到 0 个需求
2025-07-27 14:14:54 | WARNING  | app.agents.database.test_case_saver_agent:_smart_match_requirements:1059 | 智能匹配需求失败: 'RequirementRepository' object has no attribute 'search_requirements_by_keywords'
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1031 | 测试用例 8fac7158-ca30-4536-8579-1f3435cffc1c 没有找到相关需求进行关联
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 图像服务与存储服务集成测试
2025-07-27 14:14:54 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: 5f71ca64-35f9-4ca9-95f0-37e5a7f7836c
2025-07-27 14:14:54 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 89a7126e-885d-4a92-b00a-df74e58ea4dc 查询到 0 个需求
2025-07-27 14:14:54 | WARNING  | app.agents.database.test_case_saver_agent:_smart_match_requirements:1059 | 智能匹配需求失败: 'RequirementRepository' object has no attribute 'search_requirements_by_keywords'
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1031 | 测试用例 5f71ca64-35f9-4ca9-95f0-37e5a7f7836c 没有找到相关需求进行关联
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 端到端图像分析验收测试
2025-07-27 14:14:54 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: 582259c7-14bf-49b5-a46f-64a4503f7166
2025-07-27 14:14:54 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 89a7126e-885d-4a92-b00a-df74e58ea4dc 查询到 0 个需求
2025-07-27 14:14:54 | WARNING  | app.agents.database.test_case_saver_agent:_smart_match_requirements:1059 | 智能匹配需求失败: 'RequirementRepository' object has no attribute 'search_requirements_by_keywords'
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1031 | 测试用例 582259c7-14bf-49b5-a46f-64a4503f7166 没有找到相关需求进行关联
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 图像文件大小边界测试
2025-07-27 14:14:54 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: 6592322b-cbb3-491c-bb17-b7f16df6c721
2025-07-27 14:14:54 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 89a7126e-885d-4a92-b00a-df74e58ea4dc 查询到 0 个需求
2025-07-27 14:14:54 | WARNING  | app.agents.database.test_case_saver_agent:_smart_match_requirements:1059 | 智能匹配需求失败: 'RequirementRepository' object has no attribute 'search_requirements_by_keywords'
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1031 | 测试用例 6592322b-cbb3-491c-bb17-b7f16df6c721 没有找到相关需求进行关联
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 图像分析异常处理测试
2025-07-27 14:14:54 | INFO     | app.agents.database.test_case_saver_agent:_save_test_cases_to_database:335 | 批量保存完成: 成功 9 个，失败 0 个
2025-07-27 14:14:54 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: ✅ 测试用例保存完成，成功保存 9 个用例...
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 测试用例保存完成，成功保存 9 个用例
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🔄 第3步: 生成测试用例思维导图......
2025-07-27 14:14:54 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🧠 正在生成企业级测试用例思维导图......
2025-07-27 14:14:54 | INFO     | app.agents.test_case.test_case_generator_agent:_sendmind_map_request:1341 | 已发送企业级思维导图生成请求: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: ✅ 思维导图生成完成...
2025-07-27 14:14:54 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🏆 企业级测试用例处理完成！生成 9 个高质量测试用例，成功保存 9 个，质量评分: 0.80...
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第3步: 生成测试用例思维导图...
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🧠 正在生成企业级测试用例思维导图...
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-27 14:14:55 | INFO     | app.core.agents.base:__init__:47 | 初始化 思维导图生成智能体 智能体 (ID: mind_map_generator)
2025-07-27 14:14:55 | INFO     | app.agents.test_case.mind_map_generator_agent:__init__:90 | 思维导图生成智能体初始化完成: 思维导图生成智能体
2025-07-27 14:14:55 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 思维导图生成智能体
2025-07-27 14:14:55 | INFO     | app.agents.test_case.mind_map_generator_agent:handle_mind_map_generation_request:113 | 开始处理思维导图生成请求: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | DEBUG    | app.core.agents.base:_send_message:88 | [思维导图生成智能体] 发送message: 🧠 开始生成思维导图，测试用例数量: 9...
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 思维导图生成完成
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🏆 企业级测试用例处理完成！生成 9 个高质量测试用例，成功保存 9 个，质量评分: 0.80
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🧠 开始生成思维导图，测试用例数量: 9
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例保存智能体 - completion
2025-07-27 14:14:55 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例保存智能体
2025-07-27 14:14:55 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体 - progress
2025-07-27 14:14:55 | INFO     | app.agents.test_case.mind_map_generator_agent:_fetch_test_cases_data:255 | 获取到 9 个测试用例数据
2025-07-27 14:14:55 | DEBUG    | app.core.agents.base:_send_message:88 | [思维导图生成智能体] 发送message: 🎨 正在分析测试用例结构......
2025-07-27 14:14:55 | DEBUG    | app.core.agents.base:_send_message:88 | [思维导图生成智能体] 发送message: 🔗 正在生成节点和连接......
2025-07-27 14:14:55 | DEBUG    | app.core.agents.base:_send_message:88 | [思维导图生成智能体] 发送message: 📐 正在计算布局......
2025-07-27 14:14:55 | INFO     | app.agents.test_case.mind_map_generator_agent:_generate_mind_map:304 | 思维导图生成完成: 14 节点, 13 边
2025-07-27 14:14:55 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体 - info
2025-07-27 14:14:55 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🎨 正在分析测试用例结构...
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 正在生成节点和连接...
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体
2025-07-27 14:14:55 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体 - success
2025-07-27 14:14:55 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 思维导图生成智能体 - info
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📐 正在计算布局...
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体 - success
2025-07-27 14:14:55 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体
2025-07-27 14:14:55 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 思维导图生成智能体
2025-07-27 14:14:55 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 测试用例生成智能体
2025-07-27 14:14:55 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 思维导图生成智能体 - progress
2025-07-27 14:14:55 | INFO     | app.agents.test_case.mind_map_generator_agent:_save_mind_map_to_database:597 | 思维导图已保存到数据库: 53949922-e299-48df-a49f-c70e7f3330b8
2025-07-27 14:14:55 | DEBUG    | app.core.agents.base:_send_message:88 | [思维导图生成智能体] 发送message: ✅ 思维导图生成完成，包含 14 个节点...
2025-07-27 14:14:55 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 思维导图生成智能体
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 思维导图生成完成，包含 14 个节点
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | DEBUG    | app.utils.agent_message_log_utils:update_session_logs_summary:181 | 更新会话日志摘要成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:131 | 会话日志摘要已更新: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 思维导图生成智能体 - success
2025-07-27 14:14:55 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 89a7126e-885d-4a92-b00a-df74e58ea4dc - 思维导图生成智能体
2025-07-27 14:14:55 | INFO     | app.services.test_case.orchestrator_service:_cleanup_runtime:146 | 标记会话为已完成: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | WARNING  | app.agents.database.session_status_agent:update_session_status:486 | 运行时未初始化，直接调用数据库更新
2025-07-27 14:14:55 | INFO     | app.utils.session_db_utils:update_session_status:129 | 更新会话状态成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc -> completed
2025-07-27 14:14:55 | DEBUG    | app.services.test_case.orchestrator_service:_cleanup_runtime:156 | 运行时清理成功完成
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:process_file:959 | 文件处理完成，从内存中移除会话: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:process_file:977 | 文件处理任务已启动: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 17 个历史会话
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 89a7126e-885d-4a92-b00a-df74e58ea4dc: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 d818be96-fb44-4912-a1da-0922cbf4b028: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 a0d50609-59bd-44a4-aabd-9c1ffb2bcb26: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 5be0dc46-e3ae-45f8-b286-441259f20e80: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 bb00e953-0d48-4546-be96-a3ae223c7908: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 b5649e9e-375b-4001-89ab-5ce7d94d7dc8: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 9146b633-68d0-476c-b2ee-867859a3cf28: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 4e21c355-d581-4f13-a7ea-5919a25548c7: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 525fcf4e-85be-4cf9-971c-648bdadc75f6: status=processing, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 f168c0c6-401c-4fc8-b70f-80d482019260: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 47b93076-15a5-4a50-beb0-f5002e976ed3: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 817eeef3-bbea-43ac-97dc-10b4d5520abb: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ea46ee94-ad6f-4e6a-9634-d41a5df2720d: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 2d184a69-9a48-4c79-aea4-6a2d680ed278: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 dda3ccaa-a362-4f66-93b5-6c690157584f: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 0768af9c-7098-4575-b989-f3e925ec1485: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ef11c0ce-7eaf-4fda-b0f1-73d934203667: status=completed, progress=10.00
2025-07-27 15:40:02 | INFO     | main:lifespan:80 | 🔄 正在关闭系统...
2025-07-27 15:40:02 | INFO     | app.database.connection:close:127 | 数据库连接已关闭
2025-07-27 15:40:02 | INFO     | main:lifespan:85 | ✅ 数据库连接已关闭
2025-07-27 15:40:02 | INFO     | app.agents.factory:cleanup:433 | 清理智能体工厂资源...
2025-07-27 15:40:02 | INFO     | app.agents.factory:cleanup:435 | 智能体工厂资源清理完成
2025-07-27 15:40:02 | INFO     | main:lifespan:89 | ✅ 智能体资源已清理
2025-07-27 15:40:02 | INFO     | main:lifespan:91 | 👋 系统已安全关闭
