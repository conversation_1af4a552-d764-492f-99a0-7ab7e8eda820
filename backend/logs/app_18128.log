2025-08-01 17:23:04 | INFO     | app.core.logging:setup_logging:135 | 日志系统初始化完成
2025-08-01 17:23:04 | INFO     | main:lifespan:41 | 🚀 启动企业级测试用例生成系统...
2025-08-01 17:23:04 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: localhost:3306/test_case_automation
2025-08-01 17:23:04 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-01 17:23:04 | INFO     | main:lifespan:46 | ✅ 数据库连接初始化完成
2025-08-01 17:23:04 | INFO     | main:lifespan:64 | ✅ 上传目录创建完成
2025-08-01 17:23:04 | INFO     | app.agents.factory:initialize:423 | 智能体工厂初始化中...
2025-08-01 17:23:04 | INFO     | app.agents.factory:initialize:425 | 智能体工厂初始化完成
2025-08-01 17:23:04 | INFO     | main:lifespan:69 | ✅ 智能体工厂初始化完成
2025-08-01 17:23:04 | INFO     | main:lifespan:71 | 🎉 系统启动完成!
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 22 个历史会话
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 8e93c866-0f25-49fe-a6b6-3840233b0bf1: status=completed, progress=10.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 93d9466a-ee8a-4443-8f90-e0a6828bdfcf: status=created, progress=0.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 a1dac204-210e-4212-8866-704bda6f9951: status=created, progress=0.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3: status=completed, progress=10.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 8e1ce552-94ad-442b-841f-05eafc43ad8f: status=completed, progress=10.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 89a7126e-885d-4a92-b00a-df74e58ea4dc: status=completed, progress=10.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 d818be96-fb44-4912-a1da-0922cbf4b028: status=completed, progress=10.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 a0d50609-59bd-44a4-aabd-9c1ffb2bcb26: status=completed, progress=10.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 5be0dc46-e3ae-45f8-b286-441259f20e80: status=completed, progress=10.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 bb00e953-0d48-4546-be96-a3ae223c7908: status=completed, progress=10.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 b5649e9e-375b-4001-89ab-5ce7d94d7dc8: status=completed, progress=10.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 9146b633-68d0-476c-b2ee-867859a3cf28: status=completed, progress=10.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 4e21c355-d581-4f13-a7ea-5919a25548c7: status=completed, progress=10.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 525fcf4e-85be-4cf9-971c-648bdadc75f6: status=processing, progress=10.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 f168c0c6-401c-4fc8-b70f-80d482019260: status=completed, progress=10.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 47b93076-15a5-4a50-beb0-f5002e976ed3: status=completed, progress=10.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 817eeef3-bbea-43ac-97dc-10b4d5520abb: status=completed, progress=10.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ea46ee94-ad6f-4e6a-9634-d41a5df2720d: status=completed, progress=10.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 2d184a69-9a48-4c79-aea4-6a2d680ed278: status=completed, progress=10.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 dda3ccaa-a362-4f66-93b5-6c690157584f: status=completed, progress=10.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 0768af9c-7098-4575-b989-f3e925ec1485: status=completed, progress=10.00
2025-08-01 17:23:36 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ef11c0ce-7eaf-4fda-b0f1-73d934203667: status=completed, progress=10.00
2025-08-01 17:24:39 | INFO     | app.utils.session_db_utils:create_processing_session:49 | 创建处理会话记录成功: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:39 | INFO     | app.api.v1.endpoints.test_case_generator:create_generation_session:301 | 创建测试用例生成会话: 926c2068-2bd8-4b28-9c98-6da09b230de0, 类型: file
2025-08-01 17:24:40 | DEBUG    | app.api.v1.endpoints.test_case_generator:upload_file:478 | 开始更新数据库状态: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:40 | INFO     | app.utils.session_db_utils:update_session_status:129 | 更新会话状态成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 -> processing
2025-08-01 17:24:40 | DEBUG    | app.utils.session_db_utils:update_session_progress:170 | 更新会话进度成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 -> 10.0%
2025-08-01 17:24:40 | DEBUG    | app.api.v1.endpoints.test_case_generator:upload_file:496 | 数据库状态更新成功: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:40 | INFO     | app.api.v1.endpoints.test_case_generator:upload_file:504 | 文件上传成功: 926c2068-2bd8-4b28-9c98-6da09b230de0, 文件: 0729b2e6-e770-49ea-a20e-256d420f4bea.pdf, 智能体: document_parser
2025-08-01 17:24:40 | INFO     | app.utils.session_db_utils:update_session_status:129 | 更新会话状态成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 -> processing
2025-08-01 17:24:40 | INFO     | app.api.v1.endpoints.test_case_generator:process_file:829 | 文件处理开始，会话 926c2068-2bd8-4b28-9c98-6da09b230de0 状态已更新为 processing
2025-08-01 17:24:42 | INFO     | app.core.agents.collector:__init__:38 | test_case流式响应收集器初始化完成
2025-08-01 17:24:42 | DEBUG    | app.core.agents.collector:set_callback:47 | 设置流式响应回调函数
2025-08-01 17:24:42 | INFO     | app.services.test_case.orchestrator_service:__init__:66 | 测试用例智能体编排器初始化完成
2025-08-01 17:24:42 | INFO     | app.services.test_case.orchestrator_service:parse_document:325 | 开始文档解析工作流: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:42 | INFO     | app.services.test_case.orchestrator_service:initialize:76 | 🚀 初始化测试用例智能体编排器...
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 文档解析智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 图片分析智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: API规范解析智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 数据库Schema解析智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 录屏分析智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 需求解析智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试点提取智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试用例生成智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: RAG知识库检索智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 思维导图生成智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: Excel导出智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试用例保存智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 需求存储智能体
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 会话状态更新智能体
2025-08-01 17:24:42 | INFO     | app.services.test_case.orchestrator_service:_register_test_case_agents:261 | 所有测试用例智能体注册完成
2025-08-01 17:24:42 | INFO     | app.agents.factory:register_stream_collector:414 | 流式响应收集器注册成功
2025-08-01 17:24:42 | INFO     | app.services.test_case.orchestrator_service:initialize:92 | ✅ 测试用例智能体编排器初始化完成
2025-08-01 17:24:42 | INFO     | app.services.test_case.orchestrator_service:_initialize_runtime:116 | 会话已记录: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:42 | INFO     | app.services.test_case.orchestrator_service:parse_document:337 | 文档解析工作流启动完成: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:44 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-08-01 17:24:44 | INFO     | app.core.agents.base:__init__:47 | 初始化 文档解析智能体 智能体 (ID: document_parser)
2025-08-01 17:24:44 | INFO     | app.agents.test_case.document_parser_agent:__init__:61 | 文档解析智能体初始化完成: 文档解析智能体
2025-08-01 17:24:44 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 文档解析智能体
2025-08-01 17:24:44 | INFO     | app.agents.test_case.document_parser_agent:handle_document_parse_request:73 | 开始处理文档解析请求: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:44 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 🔍 开始解析文档: 0729b2e6-e770-49ea-a20e-256d420f4bea.pdf...
2025-08-01 17:24:44 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 📄 文档信息: 文件大小 138.8KB, 格式: .pdf...
2025-08-01 17:24:44 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 🔄 第1步: 开始解析文档内容......
2025-08-01 17:24:44 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 📖 正在解析 .pdf 格式文档......
2025-08-01 17:24:45 | INFO     | app.agents.test_case.document_parser_agent:_parse_pdf:227 | 开始解析PDF文档: uploads\documentsparser\20250801_172440_926c2068_0729b2e6-e770-49ea-a20e-256d420f4bea.pdf
2025-08-01 17:24:45 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 📄 PDF文档信息: 共 2 页，开始文本提取......
2025-08-01 17:24:45 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 🔍 正在提取第 1/2 页文本......
2025-08-01 17:24:45 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: ✅ 第 1 页文本提取成功 (耗时: 0.03秒, 内容: 617 字符)...
2025-08-01 17:24:45 | INFO     | app.agents.test_case.document_parser_agent:_parse_pdf:285 | 第 1 页文本提取完成，内容长度: 617
2025-08-01 17:24:45 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 🔍 正在提取第 2/2 页文本......
2025-08-01 17:24:45 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: ✅ 第 2 页文本提取成功 (耗时: 0.00秒, 内容: 433 字符)...
2025-08-01 17:24:45 | INFO     | app.agents.test_case.document_parser_agent:_parse_pdf:285 | 第 2 页文本提取完成，内容长度: 433
2025-08-01 17:24:45 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 📊 PDF文本提取统计: 成功 2 页, 失败 0 页, 总内容 1050 字符...
2025-08-01 17:24:45 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 🔄 正在整合页面内容......
2025-08-01 17:24:45 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: ✅ PDF文本解析完成! 有效页面: 2, 内容整合耗时: 0.00秒...
2025-08-01 17:24:45 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 📝 内容提取完成，耗时 0.92秒，内容长度: 1216 字符...
2025-08-01 17:24:45 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 🤖 开始AI智能分析文档内容......
2025-08-01 17:24:46 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-08-01 17:24:46 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: document_analyzer (模型: deepseek)
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 开始解析文档: 0729b2e6-e770-49ea-a20e-256d420f4bea.pdf
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📄 文档信息: 文件大小 138.8KB, 格式: .pdf
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第1步: 开始解析文档内容...
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📖 正在解析 .pdf 格式文档...
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📄 PDF文档信息: 共 2 页，开始文本提取...
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 正在提取第 1/2 页文本...
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 第 1 页文本提取成功 (耗时: 0.03秒, 内容: 617 字符)
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 正在提取第 2/2 页文本...
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 第 2 页文本提取成功 (耗时: 0.00秒, 内容: 433 字符)
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 PDF文本提取统计: 成功 2 页, 失败 0 页, 总内容 1050 字符
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 正在整合页面内容...
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:47 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体 - info
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ PDF文本解析完成! 有效页面: 2, 内容整合耗时: 0.00秒
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📝 内容提取完成，耗时 0.92秒，内容长度: 1216 字符
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🤖 开始AI智能分析文档内容...
2025-08-01 17:24:47 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:47 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体
2025-08-01 17:24:47 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体 - success
2025-08-01 17:24:47 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体
2025-08-01 17:24:47 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体 - info
2025-08-01 17:24:47 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体 - info
2025-08-01 17:24:47 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体
2025-08-01 17:24:47 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体 - progress
2025-08-01 17:24:47 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体
2025-08-01 17:24:47 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体 - success
2025-08-01 17:24:47 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体
2025-08-01 17:24:47 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体
2025-08-01 17:24:47 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体 - success
2025-08-01 17:24:47 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体 - info
2025-08-01 17:24:47 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体 - success
2025-08-01 17:24:47 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体
2025-08-01 17:24:47 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体 - success
2025-08-01 17:24:47 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体
2025-08-01 17:24:47 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体
2025-08-01 17:24:47 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体
2025-08-01 17:24:48 | ERROR    | app.agents.test_case.document_parser_agent:_run_ai_analysis:980 | AI分析执行失败: Connection error.
2025-08-01 17:24:48 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 🧠 AI分析完成，耗时 2.85秒，置信度: 0.50...
2025-08-01 17:24:48 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 📊 解析结果: 提取 0 个章节, 0 个需求...
2025-08-01 17:24:48 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 🔄 第2步: 基于文档内容生成测试用例......
2025-08-01 17:24:48 | INFO     | app.agents.test_case.document_parser_agent:_generate_test_cases_from_document:1065 | 从文档生成了 0 个测试用例
2025-08-01 17:24:48 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: ✅ 成功生成 0 个测试用例...
2025-08-01 17:24:48 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: ✅ 文档解析完成! 处理时间: 3.78秒...
2025-08-01 17:24:48 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 🔄 转发到测试点提取智能体进行专业测试点分析......
2025-08-01 17:24:48 | INFO     | app.agents.test_case.document_parser_agent:_send_to_test_point_extractor:1137 | 已发送到测试点提取智能体: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:48 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🧠 AI分析完成，耗时 2.85秒，置信度: 0.50
2025-08-01 17:24:48 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:48 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 解析结果: 提取 0 个章节, 0 个需求
2025-08-01 17:24:48 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:48 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第2步: 基于文档内容生成测试用例...
2025-08-01 17:24:48 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:48 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 成功生成 0 个测试用例
2025-08-01 17:24:48 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:48 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 文档解析完成! 处理时间: 3.78秒
2025-08-01 17:24:48 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:48 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 转发到测试点提取智能体进行专业测试点分析...
2025-08-01 17:24:48 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:49 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-08-01 17:24:49 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试点提取智能体 智能体 (ID: test_point_extractor)
2025-08-01 17:24:49 | INFO     | app.agents.test_case.test_point_extraction_agent:__init__:91 | 测试点提取智能体初始化完成: 测试点提取智能体
2025-08-01 17:24:49 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试点提取智能体
2025-08-01 17:24:49 | INFO     | app.agents.test_case.test_point_extraction_agent:handle_test_point_extraction_request:103 | 开始处理测试点提取请求: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:49 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: 🎯 开始企业级测试点提取: 基于需求解析结果...
2025-08-01 17:24:49 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: 📊 需求分析输入: 0 个需求, 0 个业务流程...
2025-08-01 17:24:49 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: 🔄 第1步: 开始专业测试点提取分析......
2025-08-01 17:24:49 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-08-01 17:24:49 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: test_point_extractor (模型: deepseek)
2025-08-01 17:24:49 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🎯 开始企业级测试点提取: 基于需求解析结果
2025-08-01 17:24:49 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:49 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体 - success
2025-08-01 17:24:49 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 需求分析输入: 0 个需求, 0 个业务流程
2025-08-01 17:24:49 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:49 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体 - metrics
2025-08-01 17:24:49 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体 - info
2025-08-01 17:24:49 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体 - success
2025-08-01 17:24:49 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第1步: 开始专业测试点提取分析...
2025-08-01 17:24:49 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:49 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体
2025-08-01 17:24:49 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体
2025-08-01 17:24:49 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体
2025-08-01 17:24:49 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体
2025-08-01 17:24:49 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体 - success
2025-08-01 17:24:49 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 文档解析智能体
2025-08-01 17:24:49 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试点提取智能体 - info
2025-08-01 17:24:49 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试点提取智能体 - info
2025-08-01 17:24:49 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试点提取智能体
2025-08-01 17:24:49 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试点提取智能体
2025-08-01 17:24:51 | ERROR    | app.agents.test_case.test_point_extraction_agent:_run_ai_extraction:704 | AI提取执行失败: Connection error.
2025-08-01 17:24:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: 📈 测试点提取完成: 功能测试点 0 个, 非功能测试点 0 个, 集成测试点 0 个, 总计 0 ...
2025-08-01 17:24:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: 🔄 第2步: 基于测试点生成测试用例......
2025-08-01 17:24:51 | INFO     | app.agents.test_case.test_point_extraction_agent:_generate_test_cases_from_test_points:895 | 从测试点生成了 0 个测试用例
2025-08-01 17:24:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: ✅ 成功生成 0 个测试用例...
2025-08-01 17:24:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: ✅ 测试点提取完成! 处理时间: 1.94秒...
2025-08-01 17:24:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: 🔄 转发到测试用例生成智能体进行用例生成......
2025-08-01 17:24:51 | INFO     | app.agents.test_case.test_point_extraction_agent:_send_to_test_case_generator:944 | 已发送测试点提取响应到测试用例生成智能体: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📈 测试点提取完成: 功能测试点 0 个, 非功能测试点 0 个, 集成测试点 0 个, 总计 0 个测试点
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第2步: 基于测试点生成测试用例...
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 成功生成 0 个测试用例
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 测试点提取完成! 处理时间: 1.94秒
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 转发到测试用例生成智能体进行用例生成...
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-08-01 17:24:51 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试用例生成智能体 智能体 (ID: test_case_generator)
2025-08-01 17:24:51 | INFO     | app.agents.test_case.test_case_generator_agent:__init__:126 | 企业级测试用例生成智能体初始化完成: 测试用例生成智能体
2025-08-01 17:24:51 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试用例生成智能体
2025-08-01 17:24:51 | INFO     | app.agents.test_case.test_case_generator_agent:handle_test_point_extraction_response:144 | 开始处理测试点提取响应: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🏭 开始企业级测试用例生成，基于专业测试点提取结果...
2025-08-01 17:24:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 📊 测试点分析: 功能测试点 0 个, 非功能测试点 0 个, 集成测试点 0 个, 总计 0 个测...
2025-08-01 17:24:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🔄 第1步: 基于测试点生成企业级测试用例......
2025-08-01 17:24:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🏭 正在基于专业测试点和RAG上下文生成企业级测试用例......
2025-08-01 17:24:51 | ERROR    | app.agents.test_case.test_case_generator_agent:_analyze_automation_feasibility:1145 | 分析自动化可行性失败: division by zero
2025-08-01 17:24:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 📊 企业级测试用例生成完成: 总计 0 个测试用例, 质量评分: 0.00...
2025-08-01 17:24:51 | INFO     | app.agents.test_case.test_case_generator_agent:_generatetest_cases_from_test_points:371 | 成功生成了 0 个企业级测试用例
2025-08-01 17:24:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: ⚠️ 未能生成任何测试用例...
2025-08-01 17:24:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: ⚠️ 未生成任何企业级测试用例...
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🏭 开始企业级测试用例生成，基于专业测试点提取结果
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试点提取智能体 - success
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 测试点分析: 功能测试点 0 个, 非功能测试点 0 个, 集成测试点 0 个, 总计 0 个测试点
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试点提取智能体 - info
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第1步: 基于测试点生成企业级测试用例...
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🏭 正在基于专业测试点和RAG上下文生成企业级测试用例...
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试点提取智能体
2025-08-01 17:24:51 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试点提取智能体 - success
2025-08-01 17:24:51 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试点提取智能体 - success
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 企业级测试用例生成完成: 总计 0 个测试用例, 质量评分: 0.00
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试点提取智能体
2025-08-01 17:24:51 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试点提取智能体 - info
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ⚠️ 未能生成任何测试用例
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ⚠️ 未生成任何企业级测试用例
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试点提取智能体
2025-08-01 17:24:51 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试点提取智能体
2025-08-01 17:24:51 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试点提取智能体
2025-08-01 17:24:51 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试用例生成智能体 - info
2025-08-01 17:24:51 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试用例生成智能体 - metrics
2025-08-01 17:24:51 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试用例生成智能体 - info
2025-08-01 17:24:51 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试用例生成智能体 - progress
2025-08-01 17:24:51 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试用例生成智能体
2025-08-01 17:24:51 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试用例生成智能体
2025-08-01 17:24:51 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试用例生成智能体
2025-08-01 17:24:51 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试用例生成智能体 - success
2025-08-01 17:24:51 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试用例生成智能体
2025-08-01 17:24:51 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试用例生成智能体 - warning
2025-08-01 17:24:51 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试用例生成智能体
2025-08-01 17:24:51 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试用例生成智能体
2025-08-01 17:24:51 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试用例生成智能体 - completion
2025-08-01 17:24:51 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: 926c2068-2bd8-4b28-9c98-6da09b230de0 - 测试用例生成智能体
2025-08-01 17:24:51 | DEBUG    | app.utils.agent_message_log_utils:update_session_logs_summary:181 | 更新会话日志摘要成功: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:131 | 会话日志摘要已更新: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | INFO     | app.services.test_case.orchestrator_service:_cleanup_runtime:146 | 标记会话为已完成: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | WARNING  | app.agents.database.session_status_agent:update_session_status:486 | 运行时未初始化，直接调用数据库更新
2025-08-01 17:24:51 | INFO     | app.utils.session_db_utils:update_session_status:129 | 更新会话状态成功: 926c2068-2bd8-4b28-9c98-6da09b230de0 -> completed
2025-08-01 17:24:51 | DEBUG    | app.services.test_case.orchestrator_service:_cleanup_runtime:156 | 运行时清理成功完成
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:process_file:959 | 文件处理完成，从内存中移除会话: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:24:51 | INFO     | app.api.v1.endpoints.test_case_generator:process_file:977 | 文件处理任务已启动: 926c2068-2bd8-4b28-9c98-6da09b230de0
2025-08-01 17:25:21 | ERROR    | app.api.v1.endpoints.test_case_generator:event_generator:1043 | 流式响应生成失败: 926c2068-2bd8-4b28-9c98-6da09b230de0 - '926c2068-2bd8-4b28-9c98-6da09b230de0'
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 23 个历史会话
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 926c2068-2bd8-4b28-9c98-6da09b230de0: status=completed, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 8e93c866-0f25-49fe-a6b6-3840233b0bf1: status=completed, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 93d9466a-ee8a-4443-8f90-e0a6828bdfcf: status=created, progress=0.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 a1dac204-210e-4212-8866-704bda6f9951: status=created, progress=0.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3: status=completed, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 8e1ce552-94ad-442b-841f-05eafc43ad8f: status=completed, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 89a7126e-885d-4a92-b00a-df74e58ea4dc: status=completed, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 d818be96-fb44-4912-a1da-0922cbf4b028: status=completed, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 a0d50609-59bd-44a4-aabd-9c1ffb2bcb26: status=completed, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 5be0dc46-e3ae-45f8-b286-441259f20e80: status=completed, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 bb00e953-0d48-4546-be96-a3ae223c7908: status=completed, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 b5649e9e-375b-4001-89ab-5ce7d94d7dc8: status=completed, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 9146b633-68d0-476c-b2ee-867859a3cf28: status=completed, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 4e21c355-d581-4f13-a7ea-5919a25548c7: status=completed, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 525fcf4e-85be-4cf9-971c-648bdadc75f6: status=processing, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 f168c0c6-401c-4fc8-b70f-80d482019260: status=completed, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 47b93076-15a5-4a50-beb0-f5002e976ed3: status=completed, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 817eeef3-bbea-43ac-97dc-10b4d5520abb: status=completed, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ea46ee94-ad6f-4e6a-9634-d41a5df2720d: status=completed, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 2d184a69-9a48-4c79-aea4-6a2d680ed278: status=completed, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 dda3ccaa-a362-4f66-93b5-6c690157584f: status=completed, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 0768af9c-7098-4575-b989-f3e925ec1485: status=completed, progress=10.00
2025-08-01 17:29:35 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ef11c0ce-7eaf-4fda-b0f1-73d934203667: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 23 个历史会话
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 926c2068-2bd8-4b28-9c98-6da09b230de0: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 8e93c866-0f25-49fe-a6b6-3840233b0bf1: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 93d9466a-ee8a-4443-8f90-e0a6828bdfcf: status=created, progress=0.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 a1dac204-210e-4212-8866-704bda6f9951: status=created, progress=0.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 8e1ce552-94ad-442b-841f-05eafc43ad8f: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 89a7126e-885d-4a92-b00a-df74e58ea4dc: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 d818be96-fb44-4912-a1da-0922cbf4b028: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 a0d50609-59bd-44a4-aabd-9c1ffb2bcb26: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 5be0dc46-e3ae-45f8-b286-441259f20e80: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 bb00e953-0d48-4546-be96-a3ae223c7908: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 b5649e9e-375b-4001-89ab-5ce7d94d7dc8: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 9146b633-68d0-476c-b2ee-867859a3cf28: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 4e21c355-d581-4f13-a7ea-5919a25548c7: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 525fcf4e-85be-4cf9-971c-648bdadc75f6: status=processing, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 f168c0c6-401c-4fc8-b70f-80d482019260: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 47b93076-15a5-4a50-beb0-f5002e976ed3: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 817eeef3-bbea-43ac-97dc-10b4d5520abb: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ea46ee94-ad6f-4e6a-9634-d41a5df2720d: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 2d184a69-9a48-4c79-aea4-6a2d680ed278: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 dda3ccaa-a362-4f66-93b5-6c690157584f: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 0768af9c-7098-4575-b989-f3e925ec1485: status=completed, progress=10.00
2025-08-01 17:29:47 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ef11c0ce-7eaf-4fda-b0f1-73d934203667: status=completed, progress=10.00
2025-08-01 17:29:53 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_project:182 | 查询到 20 个需求
2025-08-01 17:29:53 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_project:182 | 查询到 29 个需求
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 23 个历史会话
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 926c2068-2bd8-4b28-9c98-6da09b230de0: status=completed, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 8e93c866-0f25-49fe-a6b6-3840233b0bf1: status=completed, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 93d9466a-ee8a-4443-8f90-e0a6828bdfcf: status=created, progress=0.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 a1dac204-210e-4212-8866-704bda6f9951: status=created, progress=0.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3: status=completed, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 8e1ce552-94ad-442b-841f-05eafc43ad8f: status=completed, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 89a7126e-885d-4a92-b00a-df74e58ea4dc: status=completed, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 d818be96-fb44-4912-a1da-0922cbf4b028: status=completed, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 a0d50609-59bd-44a4-aabd-9c1ffb2bcb26: status=completed, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 5be0dc46-e3ae-45f8-b286-441259f20e80: status=completed, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 bb00e953-0d48-4546-be96-a3ae223c7908: status=completed, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 b5649e9e-375b-4001-89ab-5ce7d94d7dc8: status=completed, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 9146b633-68d0-476c-b2ee-867859a3cf28: status=completed, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 4e21c355-d581-4f13-a7ea-5919a25548c7: status=completed, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 525fcf4e-85be-4cf9-971c-648bdadc75f6: status=processing, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 f168c0c6-401c-4fc8-b70f-80d482019260: status=completed, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 47b93076-15a5-4a50-beb0-f5002e976ed3: status=completed, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 817eeef3-bbea-43ac-97dc-10b4d5520abb: status=completed, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ea46ee94-ad6f-4e6a-9634-d41a5df2720d: status=completed, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 2d184a69-9a48-4c79-aea4-6a2d680ed278: status=completed, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 dda3ccaa-a362-4f66-93b5-6c690157584f: status=completed, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 0768af9c-7098-4575-b989-f3e925ec1485: status=completed, progress=10.00
2025-08-01 17:31:23 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ef11c0ce-7eaf-4fda-b0f1-73d934203667: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 23 个历史会话
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 926c2068-2bd8-4b28-9c98-6da09b230de0: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 8e93c866-0f25-49fe-a6b6-3840233b0bf1: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 93d9466a-ee8a-4443-8f90-e0a6828bdfcf: status=created, progress=0.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 a1dac204-210e-4212-8866-704bda6f9951: status=created, progress=0.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 8e1ce552-94ad-442b-841f-05eafc43ad8f: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 89a7126e-885d-4a92-b00a-df74e58ea4dc: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 d818be96-fb44-4912-a1da-0922cbf4b028: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 a0d50609-59bd-44a4-aabd-9c1ffb2bcb26: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 5be0dc46-e3ae-45f8-b286-441259f20e80: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 bb00e953-0d48-4546-be96-a3ae223c7908: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 b5649e9e-375b-4001-89ab-5ce7d94d7dc8: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 9146b633-68d0-476c-b2ee-867859a3cf28: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 4e21c355-d581-4f13-a7ea-5919a25548c7: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 525fcf4e-85be-4cf9-971c-648bdadc75f6: status=processing, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 f168c0c6-401c-4fc8-b70f-80d482019260: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 47b93076-15a5-4a50-beb0-f5002e976ed3: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 817eeef3-bbea-43ac-97dc-10b4d5520abb: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ea46ee94-ad6f-4e6a-9634-d41a5df2720d: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 2d184a69-9a48-4c79-aea4-6a2d680ed278: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 dda3ccaa-a362-4f66-93b5-6c690157584f: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 0768af9c-7098-4575-b989-f3e925ec1485: status=completed, progress=10.00
2025-08-01 18:06:50 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ef11c0ce-7eaf-4fda-b0f1-73d934203667: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 23 个历史会话
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 926c2068-2bd8-4b28-9c98-6da09b230de0: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 8e93c866-0f25-49fe-a6b6-3840233b0bf1: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 93d9466a-ee8a-4443-8f90-e0a6828bdfcf: status=created, progress=0.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 a1dac204-210e-4212-8866-704bda6f9951: status=created, progress=0.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 8e1ce552-94ad-442b-841f-05eafc43ad8f: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 89a7126e-885d-4a92-b00a-df74e58ea4dc: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 d818be96-fb44-4912-a1da-0922cbf4b028: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 a0d50609-59bd-44a4-aabd-9c1ffb2bcb26: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 5be0dc46-e3ae-45f8-b286-441259f20e80: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 bb00e953-0d48-4546-be96-a3ae223c7908: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 b5649e9e-375b-4001-89ab-5ce7d94d7dc8: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 9146b633-68d0-476c-b2ee-867859a3cf28: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 4e21c355-d581-4f13-a7ea-5919a25548c7: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 525fcf4e-85be-4cf9-971c-648bdadc75f6: status=processing, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 f168c0c6-401c-4fc8-b70f-80d482019260: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 47b93076-15a5-4a50-beb0-f5002e976ed3: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 817eeef3-bbea-43ac-97dc-10b4d5520abb: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ea46ee94-ad6f-4e6a-9634-d41a5df2720d: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 2d184a69-9a48-4c79-aea4-6a2d680ed278: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 dda3ccaa-a362-4f66-93b5-6c690157584f: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 0768af9c-7098-4575-b989-f3e925ec1485: status=completed, progress=10.00
2025-08-01 18:29:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ef11c0ce-7eaf-4fda-b0f1-73d934203667: status=completed, progress=10.00
2025-08-01 18:29:52 | INFO     | main:lifespan:80 | 🔄 正在关闭系统...
2025-08-01 18:29:52 | INFO     | app.database.connection:close:127 | 数据库连接已关闭
2025-08-01 18:29:52 | INFO     | main:lifespan:85 | ✅ 数据库连接已关闭
2025-08-01 18:29:52 | INFO     | app.agents.factory:cleanup:433 | 清理智能体工厂资源...
2025-08-01 18:29:52 | INFO     | app.agents.factory:cleanup:435 | 智能体工厂资源清理完成
2025-08-01 18:29:52 | INFO     | main:lifespan:89 | ✅ 智能体资源已清理
2025-08-01 18:29:52 | INFO     | main:lifespan:91 | 👋 系统已安全关闭
