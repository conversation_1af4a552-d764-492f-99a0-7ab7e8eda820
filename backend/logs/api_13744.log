2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 16 个历史会话
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 d818be96-fb44-4912-a1da-0922cbf4b028: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 a0d50609-59bd-44a4-aabd-9c1ffb2bcb26: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 5be0dc46-e3ae-45f8-b286-441259f20e80: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 bb00e953-0d48-4546-be96-a3ae223c7908: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 b5649e9e-375b-4001-89ab-5ce7d94d7dc8: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 9146b633-68d0-476c-b2ee-867859a3cf28: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 4e21c355-d581-4f13-a7ea-5919a25548c7: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 525fcf4e-85be-4cf9-971c-648bdadc75f6: status=processing, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 f168c0c6-401c-4fc8-b70f-80d482019260: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 47b93076-15a5-4a50-beb0-f5002e976ed3: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 817eeef3-bbea-43ac-97dc-10b4d5520abb: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ea46ee94-ad6f-4e6a-9634-d41a5df2720d: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 2d184a69-9a48-4c79-aea4-6a2d680ed278: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 dda3ccaa-a362-4f66-93b5-6c690157584f: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 0768af9c-7098-4575-b989-f3e925ec1485: status=completed, progress=10.00
2025-07-27 14:08:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ef11c0ce-7eaf-4fda-b0f1-73d934203667: status=completed, progress=10.00
2025-07-27 14:08:20 | INFO     | app.api.v1.endpoints.test_case_generator:create_generation_session:301 | 创建测试用例生成会话: 89a7126e-885d-4a92-b00a-df74e58ea4dc, 类型: file
2025-07-27 14:08:21 | INFO     | app.api.v1.endpoints.test_case_generator:upload_file:504 | 文件上传成功: 89a7126e-885d-4a92-b00a-df74e58ea4dc, 文件: 4dcf934e460743b0ab9ab2c2113386fd.png, 智能体: image_analyzer
2025-07-27 14:08:21 | INFO     | app.api.v1.endpoints.test_case_generator:process_file:829 | 文件处理开始，会话 89a7126e-885d-4a92-b00a-df74e58ea4dc 状态已更新为 processing
2025-07-27 14:08:26 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 开始分析图片: 4dcf934e460743b0ab9ab2c2113386fd.png
2025-07-27 14:08:26 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:26 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 检测到图片类型: png
2025-07-27 14:08:26 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:26 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 正在进行通用图片分析...
2025-07-27 14:08:26 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:26 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 图片分析完成，已生成测试用例
2025-07-27 14:08:26 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:27 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🎯 开始企业级测试点提取: 基于需求解析结果
2025-07-27 14:08:27 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:27 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 需求分析输入: 1 个需求, 0 个业务流程
2025-07-27 14:08:27 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:08:27 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第1步: 开始专业测试点提取分析...
2025-07-27 14:08:27 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📈 测试点提取完成: 功能测试点 1 个, 非功能测试点 2 个, 集成测试点 1 个, 总计 7 个测试点
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第2步: 基于测试点生成测试用例...
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 成功生成 5 个测试用例
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 测试点提取完成! 处理时间: 75.08秒
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 转发到测试用例生成智能体进行用例生成...
2025-07-27 14:09:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🏭 开始企业级测试用例生成，基于专业测试点提取结果
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 测试点分析: 功能测试点 1 个, 非功能测试点 2 个, 集成测试点 1 个, 总计 7 个测试点
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第1步: 基于测试点生成企业级测试用例...
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🏭 正在基于专业测试点和RAG上下文生成企业级测试用例...
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📝 处理 1 个功能测试点...
2025-07-27 14:09:42 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:11:21 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ⚡ 处理 2 个非功能测试点...
2025-07-27 14:11:21 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:12:24 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 处理 1 个集成测试点...
2025-07-27 14:12:24 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:12:59 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 处理 1 个验收测试点...
2025-07-27 14:12:59 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:13:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🎯 处理 1 个边界测试点...
2025-07-27 14:13:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🚨 处理 1 个异常测试点...
2025-07-27 14:14:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 企业级测试用例生成完成: 总计 9 个测试用例, 质量评分: 0.80
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 企业级测试用例生成完成: 共生成 9 个测试用例
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第2步: 保存企业级测试用例到数据库...
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 💾 开始保存 9 个测试用例到数据库...
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 正在进行批量数据验证...
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📦 正在处理第 1/1 批数据 (9 个测试用例)...
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 测试用例保存完成，成功保存 9 个用例
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第3步: 生成测试用例思维导图...
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🧠 正在生成企业级测试用例思维导图...
2025-07-27 14:14:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 思维导图生成完成
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🏆 企业级测试用例处理完成！生成 9 个高质量测试用例，成功保存 9 个，质量评分: 0.80
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🧠 开始生成思维导图，测试用例数量: 9
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🎨 正在分析测试用例结构...
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 正在生成节点和连接...
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📐 正在计算布局...
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 思维导图生成完成，包含 14 个节点
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:process_file:959 | 文件处理完成，从内存中移除会话: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:14:55 | INFO     | app.api.v1.endpoints.test_case_generator:process_file:977 | 文件处理任务已启动: 89a7126e-885d-4a92-b00a-df74e58ea4dc
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 17 个历史会话
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 89a7126e-885d-4a92-b00a-df74e58ea4dc: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 d818be96-fb44-4912-a1da-0922cbf4b028: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 a0d50609-59bd-44a4-aabd-9c1ffb2bcb26: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 5be0dc46-e3ae-45f8-b286-441259f20e80: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 bb00e953-0d48-4546-be96-a3ae223c7908: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 b5649e9e-375b-4001-89ab-5ce7d94d7dc8: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 9146b633-68d0-476c-b2ee-867859a3cf28: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 4e21c355-d581-4f13-a7ea-5919a25548c7: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 525fcf4e-85be-4cf9-971c-648bdadc75f6: status=processing, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 f168c0c6-401c-4fc8-b70f-80d482019260: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 47b93076-15a5-4a50-beb0-f5002e976ed3: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 817eeef3-bbea-43ac-97dc-10b4d5520abb: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ea46ee94-ad6f-4e6a-9634-d41a5df2720d: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 2d184a69-9a48-4c79-aea4-6a2d680ed278: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 dda3ccaa-a362-4f66-93b5-6c690157584f: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 0768af9c-7098-4575-b989-f3e925ec1485: status=completed, progress=10.00
2025-07-27 14:31:57 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ef11c0ce-7eaf-4fda-b0f1-73d934203667: status=completed, progress=10.00
