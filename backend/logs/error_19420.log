2025-07-29 12:06:45 | ERROR    | app.agents.test_case.test_case_generator_agent:_parse_ai_json_response:667 | 第二次JSON解析也失败: Expecting ',' delimiter: line 14 column 56 (char 461)
2025-07-29 12:06:45 | ERROR    | app.agents.test_case.test_case_generator_agent:_parse_ai_json_response:668 | 清理后的响应: {
    "preconditions": "1. 系统已部署并运行正常 2. 测试用户账户已创建并激活 3. 网络连接稳定 4. 浏览器或客户端应用已安装并配置正确 5. 测试环境与生产环境隔离",
    "test_steps": [
        {
            "step_number": 1,
            "action": "打开系统登录页面",
    ...
2025-07-29 12:06:45 | ERROR    | app.agents.test_case.test_case_generator_agent:_parse_ai_json_response:682 | JSON解析过程中发生错误: 无法解析AI响应为有效JSON: Expecting ',' delimiter: line 14 column 56 (char 461)
2025-07-29 12:06:45 | ERROR    | app.agents.test_case.test_case_generator_agent:_ai_generate_detailed_test_case_content:625 | AI生成详细测试用例内容失败: 无法解析AI响应为有效JSON: Expecting ',' delimiter: line 14 column 56 (char 461)
2025-07-29 12:06:45 | ERROR    | app.agents.test_case.test_case_generator_agent:_ai_generate_detailed_test_case_content:626 | 原始AI响应: ```json
{
    "preconditions": "1. 系统已部署并运行正常 2. 测试用户账户已创建并激活 3. 网络连接稳定 4. 浏览器或客户端应用已安装并配置正确 5. 测试环境与生产环境隔离",
    "test_steps": [
        {
            "step_number": 1,
            "action": "打开系统登录页面",
            "input_data": "N/A",
            "expected_result": "登录页面正确加载，显示用户名和密码输入框",
            "notes": "验证页面元素加载完整"
        },
        {
            "step_number": 2,
            "action": "输入有效的用户名和密码",
            "input_data": "用户名: <EMAIL>, 密码: Test@1234",
            "exp
2025-07-29 12:17:47 | ERROR    | app.agents.test_case.test_case_generator_agent:_run_ai_test_case_generation:930 | AI测试用例生成执行失败: 
2025-07-29 12:17:49 | ERROR    | app.agents.test_case.test_case_generator_agent:_run_ai_test_case_generation:930 | AI测试用例生成执行失败: Connection error.
2025-07-29 12:17:51 | ERROR    | app.agents.test_case.test_case_generator_agent:_run_ai_test_case_generation:930 | AI测试用例生成执行失败: Connection error.
2025-07-29 12:17:51 | ERROR    | app.agents.test_case.test_case_generator_agent:_analyze_test_coverage:1101 | 分析测试覆盖度失败: 'NoneType' object has no attribute 'lower'
