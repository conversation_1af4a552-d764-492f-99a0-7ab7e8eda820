2025-07-30 17:41:33 | INFO     | app.agents.factory:initialize:423 | 智能体工厂初始化中...
2025-07-30 17:41:33 | INFO     | app.agents.factory:initialize:425 | 智能体工厂初始化完成
2025-07-30 18:16:40 | INFO     | app.core.agents.collector:__init__:38 | test_case流式响应收集器初始化完成
2025-07-30 18:16:40 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 文档解析智能体
2025-07-30 18:16:40 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 图片分析智能体
2025-07-30 18:16:40 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: API规范解析智能体
2025-07-30 18:16:40 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 数据库Schema解析智能体
2025-07-30 18:16:40 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 录屏分析智能体
2025-07-30 18:16:40 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 需求解析智能体
2025-07-30 18:16:40 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试点提取智能体
2025-07-30 18:16:40 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试用例生成智能体
2025-07-30 18:16:40 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: RAG知识库检索智能体
2025-07-30 18:16:40 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 思维导图生成智能体
2025-07-30 18:16:40 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: Excel导出智能体
2025-07-30 18:16:40 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试用例保存智能体
2025-07-30 18:16:40 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 需求存储智能体
2025-07-30 18:16:40 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 会话状态更新智能体
2025-07-30 18:16:40 | INFO     | app.agents.factory:register_stream_collector:414 | 流式响应收集器注册成功
2025-07-30 18:16:40 | INFO     | app.core.agents.base:__init__:47 | 初始化 文档解析智能体 智能体 (ID: document_parser)
2025-07-30 18:16:40 | INFO     | app.agents.test_case.document_parser_agent:__init__:61 | 文档解析智能体初始化完成: 文档解析智能体
2025-07-30 18:16:40 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 文档解析智能体
2025-07-30 18:16:40 | INFO     | app.agents.test_case.document_parser_agent:handle_document_parse_request:73 | 开始处理文档解析请求: 8e93c866-0f25-49fe-a6b6-3840233b0bf1
2025-07-30 18:16:41 | INFO     | app.agents.test_case.document_parser_agent:_parse_pdf:227 | 开始解析PDF文档: uploads\documentsparser\20250730_181638_8e93c866_0729b2e6-e770-49ea-a20e-256d420f4bea.pdf
2025-07-30 18:16:41 | INFO     | app.agents.test_case.document_parser_agent:_parse_pdf:285 | 第 1 页文本提取完成，内容长度: 617
2025-07-30 18:16:41 | INFO     | app.agents.test_case.document_parser_agent:_parse_pdf:285 | 第 2 页文本提取完成，内容长度: 433
2025-07-30 18:16:41 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: document_analyzer (模型: deepseek)
2025-07-30 18:17:24 | INFO     | app.agents.test_case.document_parser_agent:_generate_test_cases_from_document:1065 | 从文档生成了 7 个测试用例
2025-07-30 18:17:24 | INFO     | app.agents.test_case.document_parser_agent:_save_requirements_to_database:1177 | 已发送需求保存请求到需求存储智能体: 8e93c866-0f25-49fe-a6b6-3840233b0bf1
2025-07-30 18:17:24 | INFO     | app.agents.test_case.document_parser_agent:_send_to_test_point_extractor:1137 | 已发送到测试点提取智能体: 8e93c866-0f25-49fe-a6b6-3840233b0bf1
2025-07-30 18:17:24 | INFO     | app.core.agents.base:__init__:47 | 初始化 需求存储智能体 智能体 (ID: requirement_saver)
2025-07-30 18:17:24 | INFO     | app.agents.database.requirement_saver_agent:__init__:78 | 需求存储智能体初始化完成: 需求存储智能体
2025-07-30 18:17:24 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 需求存储智能体
2025-07-30 18:17:24 | INFO     | app.agents.database.requirement_saver_agent:handle_requirement_save_request:91 | 开始处理需求保存请求: 8e93c866-0f25-49fe-a6b6-3840233b0bf1
2025-07-30 18:17:25 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试点提取智能体 智能体 (ID: test_point_extractor)
2025-07-30 18:17:25 | INFO     | app.agents.test_case.test_point_extraction_agent:__init__:91 | 测试点提取智能体初始化完成: 测试点提取智能体
2025-07-30 18:17:25 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试点提取智能体
2025-07-30 18:17:25 | INFO     | app.agents.test_case.test_point_extraction_agent:handle_test_point_extraction_request:103 | 开始处理测试点提取请求: 8e93c866-0f25-49fe-a6b6-3840233b0bf1
2025-07-30 18:17:25 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: test_point_extractor (模型: deepseek)
2025-07-30 18:19:38 | INFO     | app.agents.test_case.test_point_extraction_agent:_generate_test_cases_from_test_points:895 | 从测试点生成了 6 个测试用例
2025-07-30 18:19:38 | INFO     | app.agents.test_case.test_point_extraction_agent:_send_to_test_case_generator:944 | 已发送测试点提取响应到测试用例生成智能体: 8e93c866-0f25-49fe-a6b6-3840233b0bf1
2025-07-30 18:19:39 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试用例生成智能体 智能体 (ID: test_case_generator)
2025-07-30 18:19:39 | INFO     | app.agents.test_case.test_case_generator_agent:__init__:126 | 企业级测试用例生成智能体初始化完成: 测试用例生成智能体
2025-07-30 18:19:39 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试用例生成智能体
2025-07-30 18:19:39 | INFO     | app.agents.test_case.test_case_generator_agent:handle_test_point_extraction_response:144 | 开始处理测试点提取响应: 8e93c866-0f25-49fe-a6b6-3840233b0bf1
2025-07-30 18:19:39 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-30 18:20:19 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-30 18:20:58 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-30 18:21:39 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-30 18:22:27 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-30 18:23:07 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-30 18:23:47 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-30 18:24:29 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-30 18:25:07 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-30 18:25:55 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-30 18:26:42 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-30 18:27:30 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-30 18:28:11 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-30 18:28:54 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-30 18:29:38 | ERROR    | app.agents.test_case.test_case_generator_agent:_analyze_test_coverage:1101 | 分析测试覆盖度失败: 'NoneType' object has no attribute 'lower'
2025-07-30 18:29:38 | INFO     | app.agents.test_case.test_case_generator_agent:_generatetest_cases_from_test_points:371 | 成功生成了 14 个企业级测试用例
2025-07-30 18:29:38 | INFO     | app.agents.test_case.test_case_generator_agent:_sendsave_request:1292 | 已发送企业级保存请求到数据库智能体: 8e93c866-0f25-49fe-a6b6-3840233b0bf1
2025-07-30 18:29:38 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试用例保存智能体 智能体 (ID: test_case_saver)
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:__init__:108 | 测试用例保存智能体初始化完成: 测试用例保存智能体
2025-07-30 18:29:38 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试用例保存智能体
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:handle_test_case_save_request:130 | 开始处理测试用例保存请求: 8e93c866-0f25-49fe-a6b6-3840233b0bf1
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 4 个需求到测试用例 e1fc76be-594b-4626-a575-6b9e93be13f9
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 基础登录功能验证 - 有效邮箱+正确密码组合
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 4 个需求到测试用例 a3e7b47c-f4f4-4af5-91e8-ba33027ca5df
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 基础登录功能验证 - 有效邮箱+错误密码组合
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 4 个需求到测试用例 c184b533-70cf-41cb-8378-de1adbfd1d92
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 基础登录功能验证 - 无效邮箱+任意密码组合
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 4 个需求到测试用例 a75dd362-870c-45d5-8a4b-07ebd4108a04
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 验证码功能测试 - 正确验证码提交
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 4 个需求到测试用例 1b882dea-2cb1-4a46-a767-3519a1edb5a2
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 验证码功能测试 - 错误验证码提交
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 4 个需求到测试用例 d0acb3ef-5d48-4051-8152-389ad45abfa9
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 验证码功能测试 - 空验证码提交
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 4 个需求到测试用例 a6f94a58-4860-49c0-809c-d00649f48caf
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 登录API性能基准测试
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 4 个需求到测试用例 50737dfc-cca9-44d2-bb31-20f8c60efe23
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 认证安全测试
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 4 个需求到测试用例 60c9b607-7a0f-4216-8d04-4c8de123b489
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 认证服务集成测试
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 4 个需求到测试用例 cd465657-6f81-4d97-8ddd-80a856046f53
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 端到端登录流程验证
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 4 个需求到测试用例 0f35d95f-5eee-4421-be79-226f022006e6
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 输入字段边界测试
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 4 个需求到测试用例 c50df113-62ad-4250-9b23-52b4f7966fae
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 异常场景测试 - 认证服务宕机时登录
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 4 个需求到测试用例 d54e4092-5958-4e67-9f7d-513b2a0adea6
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 异常场景测试 - 用户DB连接失败
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 4 个需求到测试用例 d9572fdd-5347-4c66-8be9-cdb74f8b768b
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 异常场景测试 - API响应超时
2025-07-30 18:29:38 | INFO     | app.agents.database.test_case_saver_agent:_save_test_cases_to_database:335 | 批量保存完成: 成功 14 个，失败 0 个
2025-07-30 18:29:38 | INFO     | app.agents.test_case.test_case_generator_agent:_sendmind_map_request:1341 | 已发送企业级思维导图生成请求: 8e93c866-0f25-49fe-a6b6-3840233b0bf1
2025-07-30 18:29:39 | INFO     | app.core.agents.base:__init__:47 | 初始化 思维导图生成智能体 智能体 (ID: mind_map_generator)
2025-07-30 18:29:39 | INFO     | app.agents.test_case.mind_map_generator_agent:__init__:90 | 思维导图生成智能体初始化完成: 思维导图生成智能体
2025-07-30 18:29:39 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 思维导图生成智能体
2025-07-30 18:29:39 | INFO     | app.agents.test_case.mind_map_generator_agent:handle_mind_map_generation_request:113 | 开始处理思维导图生成请求: 8e93c866-0f25-49fe-a6b6-3840233b0bf1
2025-07-30 18:29:39 | INFO     | app.agents.test_case.mind_map_generator_agent:_fetch_test_cases_data:255 | 获取到 14 个测试用例数据
2025-07-30 18:29:39 | INFO     | app.agents.test_case.mind_map_generator_agent:_generate_mind_map:304 | 思维导图生成完成: 19 节点, 18 边
2025-07-30 18:29:39 | INFO     | app.agents.test_case.mind_map_generator_agent:_save_mind_map_to_database:597 | 思维导图已保存到数据库: 439ea8aa-c08f-47d4-9f35-5b8f7a63db8a
2025-07-30 18:29:39 | WARNING  | app.agents.database.session_status_agent:update_session_status:486 | 运行时未初始化，直接调用数据库更新
2025-07-30 19:58:54 | INFO     | app.agents.factory:cleanup:433 | 清理智能体工厂资源...
2025-07-30 19:58:54 | INFO     | app.agents.factory:cleanup:435 | 智能体工厂资源清理完成
