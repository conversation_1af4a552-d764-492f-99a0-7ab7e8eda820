2025-07-29 11:52:48 | INFO     | app.core.logging:setup_logging:135 | 日志系统初始化完成
2025-07-29 11:52:49 | INFO     | main:lifespan:41 | 🚀 启动企业级测试用例生成系统...
2025-07-29 11:52:49 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: localhost:3306/test_case_automation
2025-07-29 11:52:49 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-07-29 11:52:49 | INFO     | main:lifespan:46 | ✅ 数据库连接初始化完成
2025-07-29 11:52:49 | INFO     | main:lifespan:64 | ✅ 上传目录创建完成
2025-07-29 11:52:49 | INFO     | app.agents.factory:initialize:423 | 智能体工厂初始化中...
2025-07-29 11:52:49 | INFO     | app.agents.factory:initialize:425 | 智能体工厂初始化完成
2025-07-29 11:52:49 | INFO     | main:lifespan:69 | ✅ 智能体工厂初始化完成
2025-07-29 11:52:49 | INFO     | main:lifespan:71 | 🎉 系统启动完成!
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 18 个历史会话
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 8e1ce552-94ad-442b-841f-05eafc43ad8f: status=completed, progress=10.00
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 89a7126e-885d-4a92-b00a-df74e58ea4dc: status=completed, progress=10.00
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 d818be96-fb44-4912-a1da-0922cbf4b028: status=completed, progress=10.00
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 a0d50609-59bd-44a4-aabd-9c1ffb2bcb26: status=completed, progress=10.00
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 5be0dc46-e3ae-45f8-b286-441259f20e80: status=completed, progress=10.00
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 bb00e953-0d48-4546-be96-a3ae223c7908: status=completed, progress=10.00
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 b5649e9e-375b-4001-89ab-5ce7d94d7dc8: status=completed, progress=10.00
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 9146b633-68d0-476c-b2ee-867859a3cf28: status=completed, progress=10.00
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 4e21c355-d581-4f13-a7ea-5919a25548c7: status=completed, progress=10.00
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 525fcf4e-85be-4cf9-971c-648bdadc75f6: status=processing, progress=10.00
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 f168c0c6-401c-4fc8-b70f-80d482019260: status=completed, progress=10.00
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 47b93076-15a5-4a50-beb0-f5002e976ed3: status=completed, progress=10.00
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 817eeef3-bbea-43ac-97dc-10b4d5520abb: status=completed, progress=10.00
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ea46ee94-ad6f-4e6a-9634-d41a5df2720d: status=completed, progress=10.00
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 2d184a69-9a48-4c79-aea4-6a2d680ed278: status=completed, progress=10.00
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 dda3ccaa-a362-4f66-93b5-6c690157584f: status=completed, progress=10.00
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 0768af9c-7098-4575-b989-f3e925ec1485: status=completed, progress=10.00
2025-07-29 11:53:06 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1183 | 数据库会话 ef11c0ce-7eaf-4fda-b0f1-73d934203667: status=completed, progress=10.00
2025-07-29 11:59:00 | INFO     | app.utils.session_db_utils:create_processing_session:49 | 创建处理会话记录成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:00 | INFO     | app.api.v1.endpoints.test_case_generator:create_generation_session:301 | 创建测试用例生成会话: d29774a0-6d5a-4fd0-b00c-950898c40fe3, 类型: file
2025-07-29 11:59:01 | DEBUG    | app.api.v1.endpoints.test_case_generator:upload_file:478 | 开始更新数据库状态: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:01 | INFO     | app.utils.session_db_utils:update_session_status:129 | 更新会话状态成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 -> processing
2025-07-29 11:59:01 | DEBUG    | app.utils.session_db_utils:update_session_progress:170 | 更新会话进度成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 -> 10.0%
2025-07-29 11:59:01 | DEBUG    | app.api.v1.endpoints.test_case_generator:upload_file:496 | 数据库状态更新成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:01 | INFO     | app.api.v1.endpoints.test_case_generator:upload_file:504 | 文件上传成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3, 文件: 0729b2e6-e770-49ea-a20e-256d420f4bea.pdf, 智能体: document_parser
2025-07-29 11:59:01 | INFO     | app.utils.session_db_utils:update_session_status:129 | 更新会话状态成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 -> processing
2025-07-29 11:59:01 | INFO     | app.api.v1.endpoints.test_case_generator:process_file:829 | 文件处理开始，会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3 状态已更新为 processing
2025-07-29 11:59:03 | INFO     | app.core.agents.collector:__init__:38 | test_case流式响应收集器初始化完成
2025-07-29 11:59:03 | DEBUG    | app.core.agents.collector:set_callback:47 | 设置流式响应回调函数
2025-07-29 11:59:03 | INFO     | app.services.test_case.orchestrator_service:__init__:66 | 测试用例智能体编排器初始化完成
2025-07-29 11:59:03 | INFO     | app.services.test_case.orchestrator_service:parse_document:325 | 开始文档解析工作流: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:03 | INFO     | app.services.test_case.orchestrator_service:initialize:76 | 🚀 初始化测试用例智能体编排器...
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 文档解析智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 图片分析智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: API规范解析智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 数据库Schema解析智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 录屏分析智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 需求解析智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试点提取智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试用例生成智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: RAG知识库检索智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 思维导图生成智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: Excel导出智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试用例保存智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 需求存储智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 会话状态更新智能体
2025-07-29 11:59:03 | INFO     | app.services.test_case.orchestrator_service:_register_test_case_agents:261 | 所有测试用例智能体注册完成
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_stream_collector:414 | 流式响应收集器注册成功
2025-07-29 11:59:03 | INFO     | app.services.test_case.orchestrator_service:initialize:92 | ✅ 测试用例智能体编排器初始化完成
2025-07-29 11:59:03 | INFO     | app.services.test_case.orchestrator_service:_initialize_runtime:116 | 会话已记录: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:03 | INFO     | app.services.test_case.orchestrator_service:parse_document:337 | 文档解析工作流启动完成: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:04 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 11:59:04 | INFO     | app.core.agents.base:__init__:47 | 初始化 文档解析智能体 智能体 (ID: document_parser)
2025-07-29 11:59:04 | INFO     | app.agents.test_case.document_parser_agent:__init__:61 | 文档解析智能体初始化完成: 文档解析智能体
2025-07-29 11:59:04 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 文档解析智能体
2025-07-29 11:59:04 | INFO     | app.agents.test_case.document_parser_agent:handle_document_parse_request:73 | 开始处理文档解析请求: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:04 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 🔍 开始解析文档: 0729b2e6-e770-49ea-a20e-256d420f4bea.pdf...
2025-07-29 11:59:04 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 📄 文档信息: 文件大小 138.8KB, 格式: .pdf...
2025-07-29 11:59:04 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 🔄 第1步: 开始解析文档内容......
2025-07-29 11:59:04 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 📖 正在解析 .pdf 格式文档......
2025-07-29 11:59:04 | INFO     | app.agents.test_case.document_parser_agent:_parse_pdf:227 | 开始解析PDF文档: uploads\documentsparser\20250729_115901_d29774a0_0729b2e6-e770-49ea-a20e-256d420f4bea.pdf
2025-07-29 11:59:04 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 📄 PDF文档信息: 共 2 页，开始文本提取......
2025-07-29 11:59:04 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 🔍 正在提取第 1/2 页文本......
2025-07-29 11:59:04 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: ✅ 第 1 页文本提取成功 (耗时: 0.02秒, 内容: 617 字符)...
2025-07-29 11:59:04 | INFO     | app.agents.test_case.document_parser_agent:_parse_pdf:285 | 第 1 页文本提取完成，内容长度: 617
2025-07-29 11:59:04 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 🔍 正在提取第 2/2 页文本......
2025-07-29 11:59:04 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: ✅ 第 2 页文本提取成功 (耗时: 0.00秒, 内容: 433 字符)...
2025-07-29 11:59:04 | INFO     | app.agents.test_case.document_parser_agent:_parse_pdf:285 | 第 2 页文本提取完成，内容长度: 433
2025-07-29 11:59:04 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 📊 PDF文本提取统计: 成功 2 页, 失败 0 页, 总内容 1050 字符...
2025-07-29 11:59:04 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 🔄 正在整合页面内容......
2025-07-29 11:59:04 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: ✅ PDF文本解析完成! 有效页面: 2, 内容整合耗时: 0.00秒...
2025-07-29 11:59:04 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 📝 内容提取完成，耗时 0.47秒，内容长度: 1216 字符...
2025-07-29 11:59:04 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 🤖 开始AI智能分析文档内容......
2025-07-29 11:59:05 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 11:59:05 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: document_analyzer (模型: deepseek)
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 开始解析文档: 0729b2e6-e770-49ea-a20e-256d420f4bea.pdf
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📄 文档信息: 文件大小 138.8KB, 格式: .pdf
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第1步: 开始解析文档内容...
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📖 正在解析 .pdf 格式文档...
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📄 PDF文档信息: 共 2 页，开始文本提取...
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 正在提取第 1/2 页文本...
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 第 1 页文本提取成功 (耗时: 0.02秒, 内容: 617 字符)
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 正在提取第 2/2 页文本...
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 第 2 页文本提取成功 (耗时: 0.00秒, 内容: 433 字符)
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:05 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体 - info
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 PDF文本提取统计: 成功 2 页, 失败 0 页, 总内容 1050 字符
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 正在整合页面内容...
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ PDF文本解析完成! 有效页面: 2, 内容整合耗时: 0.00秒
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:05 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📝 内容提取完成，耗时 0.47秒，内容长度: 1216 字符
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🤖 开始AI智能分析文档内容...
2025-07-29 11:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:05 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体 - success
2025-07-29 11:59:05 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体
2025-07-29 11:59:05 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体 - info
2025-07-29 11:59:05 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体
2025-07-29 11:59:05 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体 - progress
2025-07-29 11:59:05 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体
2025-07-29 11:59:05 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体 - success
2025-07-29 11:59:05 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体 - info
2025-07-29 11:59:05 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体 - success
2025-07-29 11:59:05 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体
2025-07-29 11:59:05 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体
2025-07-29 11:59:05 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体 - success
2025-07-29 11:59:05 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体
2025-07-29 11:59:05 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体
2025-07-29 11:59:05 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体 - info
2025-07-29 11:59:05 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体 - success
2025-07-29 11:59:05 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体
2025-07-29 11:59:05 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体
2025-07-29 11:59:40 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 🧠 AI分析完成，耗时 35.26秒，置信度: 0.95...
2025-07-29 11:59:40 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 📊 解析结果: 提取 2 个章节, 5 个需求...
2025-07-29 11:59:40 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 🔄 第2步: 基于文档内容生成测试用例......
2025-07-29 11:59:40 | INFO     | app.agents.test_case.document_parser_agent:_generate_test_cases_from_document:1065 | 从文档生成了 7 个测试用例
2025-07-29 11:59:40 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: ✅ 成功生成 7 个测试用例...
2025-07-29 11:59:40 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 🔄 第3步: 保存需求信息到数据库......
2025-07-29 11:59:40 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 💾 开始保存 5 个需求到数据库......
2025-07-29 11:59:40 | INFO     | app.agents.test_case.document_parser_agent:_save_requirements_to_database:1177 | 已发送需求保存请求到需求存储智能体: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:40 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: ✅ 文档解析完成! 处理时间: 35.75秒...
2025-07-29 11:59:40 | DEBUG    | app.core.agents.base:_send_message:88 | [文档解析智能体] 发送message: 🔄 转发到测试点提取智能体进行专业测试点分析......
2025-07-29 11:59:40 | INFO     | app.agents.test_case.document_parser_agent:_send_to_test_point_extractor:1137 | 已发送到测试点提取智能体: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:40 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🧠 AI分析完成，耗时 35.26秒，置信度: 0.95
2025-07-29 11:59:40 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:40 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 解析结果: 提取 2 个章节, 5 个需求
2025-07-29 11:59:40 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:40 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第2步: 基于文档内容生成测试用例...
2025-07-29 11:59:40 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:40 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 成功生成 7 个测试用例
2025-07-29 11:59:40 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:40 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第3步: 保存需求信息到数据库...
2025-07-29 11:59:40 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:40 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 💾 开始保存 5 个需求到数据库...
2025-07-29 11:59:40 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:40 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 11:59:40 | INFO     | app.core.agents.base:__init__:47 | 初始化 需求存储智能体 智能体 (ID: requirement_saver)
2025-07-29 11:59:40 | INFO     | app.agents.database.requirement_saver_agent:__init__:78 | 需求存储智能体初始化完成: 需求存储智能体
2025-07-29 11:59:40 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 需求存储智能体
2025-07-29 11:59:40 | INFO     | app.agents.database.requirement_saver_agent:handle_requirement_save_request:91 | 开始处理需求保存请求: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:40 | DEBUG    | app.core.agents.base:_send_message:88 | [需求存储智能体] 发送message: 💾 开始保存 5 个需求到数据库......
2025-07-29 11:59:40 | DEBUG    | app.core.agents.base:_send_message:88 | [需求存储智能体] 发送message: 💾 正在批量保存 5 个需求......
2025-07-29 11:59:40 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 文档解析完成! 处理时间: 35.75秒
2025-07-29 11:59:40 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:40 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 转发到测试点提取智能体进行专业测试点分析...
2025-07-29 11:59:40 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:40 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 11:59:40 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试点提取智能体 智能体 (ID: test_point_extractor)
2025-07-29 11:59:40 | INFO     | app.agents.test_case.test_point_extraction_agent:__init__:91 | 测试点提取智能体初始化完成: 测试点提取智能体
2025-07-29 11:59:40 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试点提取智能体
2025-07-29 11:59:40 | INFO     | app.agents.test_case.test_point_extraction_agent:handle_test_point_extraction_request:103 | 开始处理测试点提取请求: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:40 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: 🎯 开始企业级测试点提取: 基于需求解析结果...
2025-07-29 11:59:40 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: 📊 需求分析输入: 7 个需求, 0 个业务流程...
2025-07-29 11:59:40 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: 🔄 第1步: 开始专业测试点提取分析......
2025-07-29 11:59:41 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 11:59:41 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: test_point_extractor (模型: deepseek)
2025-07-29 11:59:41 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体 - info
2025-07-29 11:59:41 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体 - metrics
2025-07-29 11:59:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 💾 开始保存 5 个需求到数据库...
2025-07-29 11:59:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:41 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体 - success
2025-07-29 11:59:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 💾 正在批量保存 5 个需求...
2025-07-29 11:59:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🎯 开始企业级测试点提取: 基于需求解析结果
2025-07-29 11:59:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:41 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体
2025-07-29 11:59:41 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体
2025-07-29 11:59:41 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体 - info
2025-07-29 11:59:41 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体 - success
2025-07-29 11:59:41 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体 - info
2025-07-29 11:59:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 需求分析输入: 7 个需求, 0 个业务流程
2025-07-29 11:59:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:41 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体
2025-07-29 11:59:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第1步: 开始专业测试点提取分析...
2025-07-29 11:59:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:41 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体 - success
2025-07-29 11:59:41 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体
2025-07-29 11:59:41 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体
2025-07-29 11:59:41 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体
2025-07-29 11:59:41 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 文档解析智能体
2025-07-29 11:59:41 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 需求存储智能体 - info
2025-07-29 11:59:41 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 需求存储智能体 - progress
2025-07-29 11:59:41 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 需求存储智能体
2025-07-29 11:59:41 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试点提取智能体 - info
2025-07-29 11:59:41 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 需求存储智能体
2025-07-29 11:59:41 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试点提取智能体 - info
2025-07-29 11:59:41 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试点提取智能体
2025-07-29 11:59:41 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试点提取智能体
2025-07-29 11:59:41 | INFO     | app.database.repositories.requirement_repository:batch_create_requirements:146 | 批量创建需求成功: 5 个
2025-07-29 11:59:41 | DEBUG    | app.core.agents.base:_send_message:88 | [需求存储智能体] 发送message: ✅ 需求保存完成: 成功 5 个，失败 0 个...
2025-07-29 11:59:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 需求保存完成: 成功 5 个，失败 0 个
2025-07-29 11:59:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:41 | DEBUG    | app.core.agents.base:_send_message:88 | [需求存储智能体] 发送message: ✅ 需求保存成功: 5 个需求已保存到数据库...
2025-07-29 11:59:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 需求保存成功: 5 个需求已保存到数据库
2025-07-29 11:59:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:41 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 需求存储智能体 - success
2025-07-29 11:59:41 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 需求存储智能体
2025-07-29 11:59:41 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 需求存储智能体 - success
2025-07-29 11:59:41 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 需求存储智能体
2025-07-29 11:59:58 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_project:182 | 查询到 20 个需求
2025-07-29 11:59:58 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_project:182 | 查询到 25 个需求
2025-07-29 12:00:41 | INFO     | app.database.repositories.requirement_repository:get_test_cases_by_requirement:397 | 需求 5804727e-759a-457f-8621-b3c110b652c1 关联了 0 个测试用例
2025-07-29 12:00:41 | INFO     | app.database.repositories.requirement_repository:get_test_cases_by_requirement:397 | 需求 5804727e-759a-457f-8621-b3c110b652c1 关联了 0 个测试用例
2025-07-29 12:00:48 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_project:182 | 查询到 5 个需求
2025-07-29 12:00:48 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_project:182 | 查询到 25 个需求
2025-07-29 12:00:51 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_project:182 | 查询到 20 个需求
2025-07-29 12:00:51 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_project:182 | 查询到 25 个需求
2025-07-29 12:00:55 | INFO     | app.database.repositories.requirement_repository:get_test_cases_by_requirement:397 | 需求 5804727e-759a-457f-8621-b3c110b652c1 关联了 0 个测试用例
2025-07-29 12:01:02 | INFO     | app.database.repositories.requirement_repository:get_test_cases_by_requirement:397 | 需求 79f8df11-30a7-4253-a022-8396035e6bc6 关联了 0 个测试用例
2025-07-29 12:01:02 | INFO     | app.database.repositories.requirement_repository:get_test_cases_by_requirement:397 | 需求 79f8df11-30a7-4253-a022-8396035e6bc6 关联了 0 个测试用例
2025-07-29 12:01:07 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: 📈 测试点提取完成: 功能测试点 2 个, 非功能测试点 2 个, 集成测试点 1 个, 总计 8 ...
2025-07-29 12:01:07 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: 🔄 第2步: 基于测试点生成测试用例......
2025-07-29 12:01:07 | INFO     | app.agents.test_case.test_point_extraction_agent:_generate_test_cases_from_test_points:895 | 从测试点生成了 6 个测试用例
2025-07-29 12:01:07 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: ✅ 成功生成 6 个测试用例...
2025-07-29 12:01:07 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: ✅ 测试点提取完成! 处理时间: 86.42秒...
2025-07-29 12:01:07 | DEBUG    | app.core.agents.base:_send_message:88 | [测试点提取智能体] 发送message: 🔄 转发到测试用例生成智能体进行用例生成......
2025-07-29 12:01:07 | INFO     | app.agents.test_case.test_point_extraction_agent:_send_to_test_case_generator:944 | 已发送测试点提取响应到测试用例生成智能体: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:01:07 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📈 测试点提取完成: 功能测试点 2 个, 非功能测试点 2 个, 集成测试点 1 个, 总计 8 个测试点
2025-07-29 12:01:07 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:01:07 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第2步: 基于测试点生成测试用例...
2025-07-29 12:01:07 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:01:07 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 成功生成 6 个测试用例
2025-07-29 12:01:07 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:01:07 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 测试点提取完成! 处理时间: 86.42秒
2025-07-29 12:01:07 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:01:07 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 转发到测试用例生成智能体进行用例生成...
2025-07-29 12:01:07 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:01:07 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 12:01:07 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试用例生成智能体 智能体 (ID: test_case_generator)
2025-07-29 12:01:07 | INFO     | app.agents.test_case.test_case_generator_agent:__init__:126 | 企业级测试用例生成智能体初始化完成: 测试用例生成智能体
2025-07-29 12:01:07 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试用例生成智能体
2025-07-29 12:01:07 | INFO     | app.agents.test_case.test_case_generator_agent:handle_test_point_extraction_response:144 | 开始处理测试点提取响应: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:01:07 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🏭 开始企业级测试用例生成，基于专业测试点提取结果...
2025-07-29 12:01:07 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 📊 测试点分析: 功能测试点 2 个, 非功能测试点 2 个, 集成测试点 1 个, 总计 8 个测...
2025-07-29 12:01:07 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🔄 第1步: 基于测试点生成企业级测试用例......
2025-07-29 12:01:07 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🏭 正在基于专业测试点和RAG上下文生成企业级测试用例......
2025-07-29 12:01:07 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 📝 处理 2 个功能测试点......
2025-07-29 12:01:08 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 12:01:08 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:01:08 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🏭 开始企业级测试用例生成，基于专业测试点提取结果
2025-07-29 12:01:08 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:01:08 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试点提取智能体 - success
2025-07-29 12:01:08 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 测试点分析: 功能测试点 2 个, 非功能测试点 2 个, 集成测试点 1 个, 总计 8 个测试点
2025-07-29 12:01:08 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:01:08 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试点提取智能体 - info
2025-07-29 12:01:08 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试点提取智能体 - success
2025-07-29 12:01:08 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第1步: 基于测试点生成企业级测试用例...
2025-07-29 12:01:08 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:01:08 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🏭 正在基于专业测试点和RAG上下文生成企业级测试用例...
2025-07-29 12:01:08 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:01:08 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试点提取智能体
2025-07-29 12:01:08 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📝 处理 2 个功能测试点...
2025-07-29 12:01:08 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:01:08 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试点提取智能体
2025-07-29 12:01:08 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试点提取智能体
2025-07-29 12:01:08 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试点提取智能体 - success
2025-07-29 12:01:08 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试点提取智能体 - info
2025-07-29 12:01:08 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体 - info
2025-07-29 12:01:08 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试点提取智能体
2025-07-29 12:01:08 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体 - metrics
2025-07-29 12:01:08 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试点提取智能体
2025-07-29 12:01:08 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体
2025-07-29 12:01:08 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体 - info
2025-07-29 12:01:08 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体
2025-07-29 12:01:08 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体
2025-07-29 12:01:08 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体 - progress
2025-07-29 12:01:08 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体
2025-07-29 12:01:35 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 12:01:35 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:02:03 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 12:02:03 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:02:41 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 12:02:41 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:03:12 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 12:03:12 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:03:49 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 12:03:49 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:04:25 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: ⚡ 处理 2 个非功能测试点......
2025-07-29 12:04:25 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 12:04:25 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:04:25 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ⚡ 处理 2 个非功能测试点...
2025-07-29 12:04:25 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:05:01 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 12:05:01 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:05:34 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🔗 处理 1 个集成测试点......
2025-07-29 12:05:35 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 12:05:35 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:05:35 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 处理 1 个集成测试点...
2025-07-29 12:05:35 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:06:01 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: ✅ 处理 1 个验收测试点......
2025-07-29 12:06:02 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 12:06:02 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:06:02 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 处理 1 个验收测试点...
2025-07-29 12:06:02 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:06:45 | WARNING  | app.agents.test_case.test_case_generator_agent:_parse_ai_json_response:648 | 第一次JSON解析失败: Expecting ',' delimiter: line 68 column 32 (char 2215)
2025-07-29 12:06:45 | ERROR    | app.agents.test_case.test_case_generator_agent:_parse_ai_json_response:667 | 第二次JSON解析也失败: Expecting ',' delimiter: line 14 column 56 (char 461)
2025-07-29 12:06:45 | ERROR    | app.agents.test_case.test_case_generator_agent:_parse_ai_json_response:668 | 清理后的响应: {
    "preconditions": "1. 系统已部署并运行正常 2. 测试用户账户已创建并激活 3. 网络连接稳定 4. 浏览器或客户端应用已安装并配置正确 5. 测试环境与生产环境隔离",
    "test_steps": [
        {
            "step_number": 1,
            "action": "打开系统登录页面",
    ...
2025-07-29 12:06:45 | ERROR    | app.agents.test_case.test_case_generator_agent:_parse_ai_json_response:682 | JSON解析过程中发生错误: 无法解析AI响应为有效JSON: Expecting ',' delimiter: line 14 column 56 (char 461)
2025-07-29 12:06:45 | ERROR    | app.agents.test_case.test_case_generator_agent:_ai_generate_detailed_test_case_content:625 | AI生成详细测试用例内容失败: 无法解析AI响应为有效JSON: Expecting ',' delimiter: line 14 column 56 (char 461)
2025-07-29 12:06:45 | ERROR    | app.agents.test_case.test_case_generator_agent:_ai_generate_detailed_test_case_content:626 | 原始AI响应: ```json
{
    "preconditions": "1. 系统已部署并运行正常 2. 测试用户账户已创建并激活 3. 网络连接稳定 4. 浏览器或客户端应用已安装并配置正确 5. 测试环境与生产环境隔离",
    "test_steps": [
        {
            "step_number": 1,
            "action": "打开系统登录页面",
            "input_data": "N/A",
            "expected_result": "登录页面正确加载，显示用户名和密码输入框",
            "notes": "验证页面元素加载完整"
        },
        {
            "step_number": 2,
            "action": "输入有效的用户名和密码",
            "input_data": "用户名: <EMAIL>, 密码: Test@1234",
            "exp
2025-07-29 12:06:45 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🎯 处理 1 个边界测试点......
2025-07-29 12:06:45 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 12:06:45 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:06:45 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🎯 处理 1 个边界测试点...
2025-07-29 12:06:45 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:07:20 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🚨 处理 1 个异常测试点......
2025-07-29 12:07:20 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 12:07:20 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:07:20 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🚨 处理 1 个异常测试点...
2025-07-29 12:07:20 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:47 | ERROR    | app.agents.test_case.test_case_generator_agent:_run_ai_test_case_generation:930 | AI测试用例生成执行失败: 
2025-07-29 12:17:48 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 12:17:48 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:17:49 | ERROR    | app.agents.test_case.test_case_generator_agent:_run_ai_test_case_generation:930 | AI测试用例生成执行失败: Connection error.
2025-07-29 12:17:49 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 12:17:49 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:17:51 | ERROR    | app.agents.test_case.test_case_generator_agent:_run_ai_test_case_generation:930 | AI测试用例生成执行失败: Connection error.
2025-07-29 12:17:51 | ERROR    | app.agents.test_case.test_case_generator_agent:_analyze_test_coverage:1101 | 分析测试覆盖度失败: 'NoneType' object has no attribute 'lower'
2025-07-29 12:17:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 📊 企业级测试用例生成完成: 总计 14 个测试用例, 质量评分: 0.80...
2025-07-29 12:17:51 | INFO     | app.agents.test_case.test_case_generator_agent:_generatetest_cases_from_test_points:371 | 成功生成了 14 个企业级测试用例
2025-07-29 12:17:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: ✅ 企业级测试用例生成完成: 共生成 14 个测试用例...
2025-07-29 12:17:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🔄 第2步: 保存企业级测试用例到数据库......
2025-07-29 12:17:51 | INFO     | app.agents.test_case.test_case_generator_agent:_sendsave_request:1292 | 已发送企业级保存请求到数据库智能体: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 企业级测试用例生成完成: 总计 14 个测试用例, 质量评分: 0.80
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 企业级测试用例生成完成: 共生成 14 个测试用例
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第2步: 保存企业级测试用例到数据库...
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:51 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 12:17:51 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试用例保存智能体 智能体 (ID: test_case_saver)
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:__init__:108 | 测试用例保存智能体初始化完成: 测试用例保存智能体
2025-07-29 12:17:51 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试用例保存智能体
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:handle_test_case_save_request:130 | 开始处理测试用例保存请求: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 💾 开始保存 14 个测试用例到数据库......
2025-07-29 12:17:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 🔍 正在进行批量数据验证......
2025-07-29 12:17:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 📦 正在处理第 1/1 批数据 (14 个测试用例)......
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 💾 开始保存 14 个测试用例到数据库...
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 正在进行批量数据验证...
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📦 正在处理第 1/1 批数据 (14 个测试用例)...
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:51 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体 - success
2025-07-29 12:17:51 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体 - success
2025-07-29 12:17:51 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体 - info
2025-07-29 12:17:51 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体
2025-07-29 12:17:51 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例保存智能体 - info
2025-07-29 12:17:51 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体
2025-07-29 12:17:51 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体
2025-07-29 12:17:51 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例保存智能体
2025-07-29 12:17:51 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: 04f40386-e207-4af0-a0bf-57be6455772f
2025-07-29 12:17:51 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3 查询到 5 个需求
2025-07-29 12:17:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 🔗 正在关联 5 个需求到测试用例......
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 正在关联 5 个需求到测试用例...
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:51 | INFO     | app.database.repositories.requirement_repository:batch_create_test_case_requirements:344 | 批量创建测试用例需求关联成功: 5 个
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 04f40386-e207-4af0-a0bf-57be6455772f
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 基础登录功能验证 - 有效email+正确密码
2025-07-29 12:17:51 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: b78e5504-2b18-43ef-934b-fa69099b60e5
2025-07-29 12:17:51 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3 查询到 5 个需求
2025-07-29 12:17:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 🔗 正在关联 5 个需求到测试用例......
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 正在关联 5 个需求到测试用例...
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:51 | INFO     | app.database.repositories.requirement_repository:batch_create_test_case_requirements:344 | 批量创建测试用例需求关联成功: 5 个
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 b78e5504-2b18-43ef-934b-fa69099b60e5
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 基础登录功能验证 - 有效email+错误密码
2025-07-29 12:17:51 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: fa98622b-6630-4a1b-b0fe-69d65010fb39
2025-07-29 12:17:51 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3 查询到 5 个需求
2025-07-29 12:17:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 🔗 正在关联 5 个需求到测试用例......
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 正在关联 5 个需求到测试用例...
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:51 | INFO     | app.database.repositories.requirement_repository:batch_create_test_case_requirements:344 | 批量创建测试用例需求关联成功: 5 个
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 fa98622b-6630-4a1b-b0fe-69d65010fb39
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 基础登录功能验证 - 无效email格式+任意密码
2025-07-29 12:17:51 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: 40ac8e23-153b-4fe1-8718-cfc6e2c72811
2025-07-29 12:17:51 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3 查询到 5 个需求
2025-07-29 12:17:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 🔗 正在关联 5 个需求到测试用例......
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 正在关联 5 个需求到测试用例...
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:51 | INFO     | app.database.repositories.requirement_repository:batch_create_test_case_requirements:344 | 批量创建测试用例需求关联成功: 5 个
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 40ac8e23-153b-4fe1-8718-cfc6e2c72811
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 验证码业务流程测试 - 提交正确验证码
2025-07-29 12:17:51 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: 8dac6058-46c8-4552-b8ff-f9a9f4bcf879
2025-07-29 12:17:51 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3 查询到 5 个需求
2025-07-29 12:17:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 🔗 正在关联 5 个需求到测试用例......
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 正在关联 5 个需求到测试用例...
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:51 | INFO     | app.database.repositories.requirement_repository:batch_create_test_case_requirements:344 | 批量创建测试用例需求关联成功: 5 个
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 8dac6058-46c8-4552-b8ff-f9a9f4bcf879
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 验证码业务流程测试 - 提交错误验证码
2025-07-29 12:17:51 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: a1bca865-2779-462d-94df-0b0d29a1c7a0
2025-07-29 12:17:51 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3 查询到 5 个需求
2025-07-29 12:17:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 🔗 正在关联 5 个需求到测试用例......
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 正在关联 5 个需求到测试用例...
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:51 | INFO     | app.database.repositories.requirement_repository:batch_create_test_case_requirements:344 | 批量创建测试用例需求关联成功: 5 个
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 a1bca865-2779-462d-94df-0b0d29a1c7a0
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 验证码业务流程测试 - 缺失验证码参数
2025-07-29 12:17:51 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: b16245e4-d875-4ed9-b613-32d4b60c9c52
2025-07-29 12:17:51 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3 查询到 5 个需求
2025-07-29 12:17:51 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 🔗 正在关联 5 个需求到测试用例......
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 正在关联 5 个需求到测试用例...
2025-07-29 12:17:51 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | INFO     | app.database.repositories.requirement_repository:batch_create_test_case_requirements:344 | 批量创建测试用例需求关联成功: 5 个
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 b16245e4-d875-4ed9-b613-32d4b60c9c52
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 高并发登录性能测试
2025-07-29 12:17:52 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: 0debb140-9dc6-448a-b2cc-f938b40ad57e
2025-07-29 12:17:52 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3 查询到 5 个需求
2025-07-29 12:17:52 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 🔗 正在关联 5 个需求到测试用例......
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 正在关联 5 个需求到测试用例...
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | INFO     | app.database.repositories.requirement_repository:batch_create_test_case_requirements:344 | 批量创建测试用例需求关联成功: 5 个
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 0debb140-9dc6-448a-b2cc-f938b40ad57e
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 敏感数据安全测试
2025-07-29 12:17:52 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: 6e5384e6-5fdb-44bb-9b39-cfc065e47793
2025-07-29 12:17:52 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3 查询到 5 个需求
2025-07-29 12:17:52 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 🔗 正在关联 5 个需求到测试用例......
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 正在关联 5 个需求到测试用例...
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | INFO     | app.database.repositories.requirement_repository:batch_create_test_case_requirements:344 | 批量创建测试用例需求关联成功: 5 个
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 6e5384e6-5fdb-44bb-9b39-cfc065e47793
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 认证服务与用户数据集成
2025-07-29 12:17:52 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: 4f2a6bf2-674e-43ea-a085-f5e4d86a6481
2025-07-29 12:17:52 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3 查询到 5 个需求
2025-07-29 12:17:52 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 🔗 正在关联 5 个需求到测试用例......
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 正在关联 5 个需求到测试用例...
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | INFO     | app.database.repositories.requirement_repository:batch_create_test_case_requirements:344 | 批量创建测试用例需求关联成功: 5 个
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 4f2a6bf2-674e-43ea-a085-f5e4d86a6481
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 终端用户登录体验测试
2025-07-29 12:17:52 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: 336cac7e-cdd4-4ce6-94d8-4440e3ddbda0
2025-07-29 12:17:52 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3 查询到 5 个需求
2025-07-29 12:17:52 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 🔗 正在关联 5 个需求到测试用例......
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 正在关联 5 个需求到测试用例...
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | INFO     | app.database.repositories.requirement_repository:batch_create_test_case_requirements:344 | 批量创建测试用例需求关联成功: 5 个
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 336cac7e-cdd4-4ce6-94d8-4440e3ddbda0
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 输入字段边界测试
2025-07-29 12:17:52 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: f22975c9-75e6-46d7-96e9-1916949f906b
2025-07-29 12:17:52 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3 查询到 5 个需求
2025-07-29 12:17:52 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 🔗 正在关联 5 个需求到测试用例......
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 正在关联 5 个需求到测试用例...
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | INFO     | app.database.repositories.requirement_repository:batch_create_test_case_requirements:344 | 批量创建测试用例需求关联成功: 5 个
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 f22975c9-75e6-46d7-96e9-1916949f906b
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 服务异常容错测试 - 用户查询超时
2025-07-29 12:17:52 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: 80ef5359-41a6-4e8a-b6a7-6837c507fb77
2025-07-29 12:17:52 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3 查询到 5 个需求
2025-07-29 12:17:52 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 🔗 正在关联 5 个需求到测试用例......
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 正在关联 5 个需求到测试用例...
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | INFO     | app.database.repositories.requirement_repository:batch_create_test_case_requirements:344 | 批量创建测试用例需求关联成功: 5 个
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 80ef5359-41a6-4e8a-b6a7-6837c507fb77
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 服务异常容错测试 - token存储失败
2025-07-29 12:17:52 | INFO     | app.database.repositories.test_case_repository:create_test_case:106 | 创建测试用例成功: 93178d15-4c87-459f-8f9e-ef95d9c87cc0
2025-07-29 12:17:52 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_session:202 | 会话 d29774a0-6d5a-4fd0-b00c-950898c40fe3 查询到 5 个需求
2025-07-29 12:17:52 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: 🔗 正在关联 5 个需求到测试用例......
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 正在关联 5 个需求到测试用例...
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | INFO     | app.database.repositories.requirement_repository:batch_create_test_case_requirements:344 | 批量创建测试用例需求关联成功: 5 个
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 93178d15-4c87-459f-8f9e-ef95d9c87cc0
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 服务异常容错测试 - 验证码服务500错误
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_cases_to_database:335 | 批量保存完成: 成功 14 个，失败 0 个
2025-07-29 12:17:52 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例保存智能体] 发送message: ✅ 测试用例保存完成，成功保存 14 个用例...
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 测试用例保存完成，成功保存 14 个用例
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🔄 第3步: 生成测试用例思维导图......
2025-07-29 12:17:52 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🧠 正在生成企业级测试用例思维导图......
2025-07-29 12:17:52 | INFO     | app.agents.test_case.test_case_generator_agent:_sendmind_map_request:1341 | 已发送企业级思维导图生成请求: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: ✅ 思维导图生成完成...
2025-07-29 12:17:52 | DEBUG    | app.core.agents.base:_send_message:88 | [测试用例生成智能体] 发送message: 🏆 企业级测试用例处理完成！生成 14 个高质量测试用例，成功保存 14 个，质量评分: 0.80...
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第3步: 生成测试用例思维导图...
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🧠 正在生成企业级测试用例思维导图...
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | INFO     | app.core.llms:get_deepseek_model_client:28 | DeepSeek模型客户端创建成功
2025-07-29 12:17:52 | INFO     | app.core.agents.base:__init__:47 | 初始化 思维导图生成智能体 智能体 (ID: mind_map_generator)
2025-07-29 12:17:52 | INFO     | app.agents.test_case.mind_map_generator_agent:__init__:90 | 思维导图生成智能体初始化完成: 思维导图生成智能体
2025-07-29 12:17:52 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 思维导图生成智能体
2025-07-29 12:17:52 | INFO     | app.agents.test_case.mind_map_generator_agent:handle_mind_map_generation_request:113 | 开始处理思维导图生成请求: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | DEBUG    | app.core.agents.base:_send_message:88 | [思维导图生成智能体] 发送message: 🧠 开始生成思维导图，测试用例数量: 14...
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 思维导图生成完成
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🏆 企业级测试用例处理完成！生成 14 个高质量测试用例，成功保存 14 个，质量评分: 0.80
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例保存智能体 - completion
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🧠 开始生成思维导图，测试用例数量: 14
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例保存智能体
2025-07-29 12:17:52 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体 - progress
2025-07-29 12:17:52 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体 - info
2025-07-29 12:17:52 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体
2025-07-29 12:17:52 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体 - success
2025-07-29 12:17:52 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体
2025-07-29 12:17:52 | INFO     | app.agents.test_case.mind_map_generator_agent:_fetch_test_cases_data:255 | 获取到 14 个测试用例数据
2025-07-29 12:17:52 | DEBUG    | app.core.agents.base:_send_message:88 | [思维导图生成智能体] 发送message: 🎨 正在分析测试用例结构......
2025-07-29 12:17:52 | DEBUG    | app.core.agents.base:_send_message:88 | [思维导图生成智能体] 发送message: 🔗 正在生成节点和连接......
2025-07-29 12:17:52 | DEBUG    | app.core.agents.base:_send_message:88 | [思维导图生成智能体] 发送message: 📐 正在计算布局......
2025-07-29 12:17:52 | INFO     | app.agents.test_case.mind_map_generator_agent:_generate_mind_map:304 | 思维导图生成完成: 18 节点, 17 边
2025-07-29 12:17:52 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体 - success
2025-07-29 12:17:52 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体
2025-07-29 12:17:52 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 思维导图生成智能体 - info
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🎨 正在分析测试用例结构...
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 测试用例生成智能体
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 正在生成节点和连接...
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 思维导图生成智能体
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📐 正在计算布局...
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | INFO     | app.agents.test_case.mind_map_generator_agent:_save_mind_map_to_database:597 | 思维导图已保存到数据库: 59012aef-73e5-4e91-b244-efe0edb1c3d3
2025-07-29 12:17:52 | DEBUG    | app.core.agents.base:_send_message:88 | [思维导图生成智能体] 发送message: ✅ 思维导图生成完成，包含 18 个节点...
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 思维导图生成完成，包含 18 个节点
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 思维导图生成智能体 - progress
2025-07-29 12:17:52 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 思维导图生成智能体
2025-07-29 12:17:52 | DEBUG    | app.utils.agent_message_log_utils:update_session_logs_summary:181 | 更新会话日志摘要成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:131 | 会话日志摘要已更新: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | DEBUG    | app.utils.agent_message_log_utils:save_agent_message_log:75 | 保存智能体消息日志成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 思维导图生成智能体 - success
2025-07-29 12:17:52 | DEBUG    | app.api.v1.endpoints.test_case_generator:_save_message_to_database:124 | 智能体消息已保存到数据库: d29774a0-6d5a-4fd0-b00c-950898c40fe3 - 思维导图生成智能体
2025-07-29 12:17:52 | INFO     | app.services.test_case.orchestrator_service:_cleanup_runtime:146 | 标记会话为已完成: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | WARNING  | app.agents.database.session_status_agent:update_session_status:486 | 运行时未初始化，直接调用数据库更新
2025-07-29 12:17:52 | INFO     | app.utils.session_db_utils:update_session_status:129 | 更新会话状态成功: d29774a0-6d5a-4fd0-b00c-950898c40fe3 -> completed
2025-07-29 12:17:52 | DEBUG    | app.services.test_case.orchestrator_service:_cleanup_runtime:156 | 运行时清理成功完成
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:process_file:959 | 文件处理完成，从内存中移除会话: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | INFO     | app.api.v1.endpoints.test_case_generator:process_file:977 | 文件处理任务已启动: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 14:47:07 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_project:182 | 查询到 20 个需求
2025-07-29 14:47:07 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_project:182 | 查询到 25 个需求
2025-07-29 15:23:51 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_project:182 | 查询到 20 个需求
2025-07-29 15:23:51 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_project:182 | 查询到 25 个需求
2025-07-29 15:34:00 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_project:182 | 查询到 20 个需求
2025-07-29 15:34:00 | INFO     | app.database.repositories.requirement_repository:get_requirements_by_project:182 | 查询到 25 个需求
2025-07-29 15:43:28 | INFO     | main:lifespan:80 | 🔄 正在关闭系统...
2025-07-29 15:43:28 | INFO     | app.database.connection:close:127 | 数据库连接已关闭
2025-07-29 15:43:28 | INFO     | main:lifespan:85 | ✅ 数据库连接已关闭
2025-07-29 15:43:28 | INFO     | app.agents.factory:cleanup:433 | 清理智能体工厂资源...
2025-07-29 15:43:28 | INFO     | app.agents.factory:cleanup:435 | 智能体工厂资源清理完成
2025-07-29 15:43:28 | INFO     | main:lifespan:89 | ✅ 智能体资源已清理
2025-07-29 15:43:28 | INFO     | main:lifespan:91 | 👋 系统已安全关闭
