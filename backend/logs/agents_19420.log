2025-07-29 11:52:49 | INFO     | app.agents.factory:initialize:423 | 智能体工厂初始化中...
2025-07-29 11:52:49 | INFO     | app.agents.factory:initialize:425 | 智能体工厂初始化完成
2025-07-29 11:59:03 | INFO     | app.core.agents.collector:__init__:38 | test_case流式响应收集器初始化完成
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 文档解析智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 图片分析智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: API规范解析智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 数据库Schema解析智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 录屏分析智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 需求解析智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试点提取智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试用例生成智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: RAG知识库检索智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 思维导图生成智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: Excel导出智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 测试用例保存智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 需求存储智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_agent_to_runtime:381 | 智能体注册成功: 会话状态更新智能体
2025-07-29 11:59:03 | INFO     | app.agents.factory:register_stream_collector:414 | 流式响应收集器注册成功
2025-07-29 11:59:04 | INFO     | app.core.agents.base:__init__:47 | 初始化 文档解析智能体 智能体 (ID: document_parser)
2025-07-29 11:59:04 | INFO     | app.agents.test_case.document_parser_agent:__init__:61 | 文档解析智能体初始化完成: 文档解析智能体
2025-07-29 11:59:04 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 文档解析智能体
2025-07-29 11:59:04 | INFO     | app.agents.test_case.document_parser_agent:handle_document_parse_request:73 | 开始处理文档解析请求: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:04 | INFO     | app.agents.test_case.document_parser_agent:_parse_pdf:227 | 开始解析PDF文档: uploads\documentsparser\20250729_115901_d29774a0_0729b2e6-e770-49ea-a20e-256d420f4bea.pdf
2025-07-29 11:59:04 | INFO     | app.agents.test_case.document_parser_agent:_parse_pdf:285 | 第 1 页文本提取完成，内容长度: 617
2025-07-29 11:59:04 | INFO     | app.agents.test_case.document_parser_agent:_parse_pdf:285 | 第 2 页文本提取完成，内容长度: 433
2025-07-29 11:59:05 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: document_analyzer (模型: deepseek)
2025-07-29 11:59:40 | INFO     | app.agents.test_case.document_parser_agent:_generate_test_cases_from_document:1065 | 从文档生成了 7 个测试用例
2025-07-29 11:59:40 | INFO     | app.agents.test_case.document_parser_agent:_save_requirements_to_database:1177 | 已发送需求保存请求到需求存储智能体: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:40 | INFO     | app.agents.test_case.document_parser_agent:_send_to_test_point_extractor:1137 | 已发送到测试点提取智能体: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:40 | INFO     | app.core.agents.base:__init__:47 | 初始化 需求存储智能体 智能体 (ID: requirement_saver)
2025-07-29 11:59:40 | INFO     | app.agents.database.requirement_saver_agent:__init__:78 | 需求存储智能体初始化完成: 需求存储智能体
2025-07-29 11:59:40 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 需求存储智能体
2025-07-29 11:59:40 | INFO     | app.agents.database.requirement_saver_agent:handle_requirement_save_request:91 | 开始处理需求保存请求: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:40 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试点提取智能体 智能体 (ID: test_point_extractor)
2025-07-29 11:59:40 | INFO     | app.agents.test_case.test_point_extraction_agent:__init__:91 | 测试点提取智能体初始化完成: 测试点提取智能体
2025-07-29 11:59:40 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试点提取智能体
2025-07-29 11:59:40 | INFO     | app.agents.test_case.test_point_extraction_agent:handle_test_point_extraction_request:103 | 开始处理测试点提取请求: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 11:59:41 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: test_point_extractor (模型: deepseek)
2025-07-29 12:01:07 | INFO     | app.agents.test_case.test_point_extraction_agent:_generate_test_cases_from_test_points:895 | 从测试点生成了 6 个测试用例
2025-07-29 12:01:07 | INFO     | app.agents.test_case.test_point_extraction_agent:_send_to_test_case_generator:944 | 已发送测试点提取响应到测试用例生成智能体: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:01:07 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试用例生成智能体 智能体 (ID: test_case_generator)
2025-07-29 12:01:07 | INFO     | app.agents.test_case.test_case_generator_agent:__init__:126 | 企业级测试用例生成智能体初始化完成: 测试用例生成智能体
2025-07-29 12:01:07 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试用例生成智能体
2025-07-29 12:01:07 | INFO     | app.agents.test_case.test_case_generator_agent:handle_test_point_extraction_response:144 | 开始处理测试点提取响应: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:01:08 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:01:35 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:02:03 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:02:41 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:03:12 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:03:49 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:04:25 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:05:01 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:05:35 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:06:02 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:06:45 | WARNING  | app.agents.test_case.test_case_generator_agent:_parse_ai_json_response:648 | 第一次JSON解析失败: Expecting ',' delimiter: line 68 column 32 (char 2215)
2025-07-29 12:06:45 | ERROR    | app.agents.test_case.test_case_generator_agent:_parse_ai_json_response:667 | 第二次JSON解析也失败: Expecting ',' delimiter: line 14 column 56 (char 461)
2025-07-29 12:06:45 | ERROR    | app.agents.test_case.test_case_generator_agent:_parse_ai_json_response:668 | 清理后的响应: {
    "preconditions": "1. 系统已部署并运行正常 2. 测试用户账户已创建并激活 3. 网络连接稳定 4. 浏览器或客户端应用已安装并配置正确 5. 测试环境与生产环境隔离",
    "test_steps": [
        {
            "step_number": 1,
            "action": "打开系统登录页面",
    ...
2025-07-29 12:06:45 | ERROR    | app.agents.test_case.test_case_generator_agent:_parse_ai_json_response:682 | JSON解析过程中发生错误: 无法解析AI响应为有效JSON: Expecting ',' delimiter: line 14 column 56 (char 461)
2025-07-29 12:06:45 | ERROR    | app.agents.test_case.test_case_generator_agent:_ai_generate_detailed_test_case_content:625 | AI生成详细测试用例内容失败: 无法解析AI响应为有效JSON: Expecting ',' delimiter: line 14 column 56 (char 461)
2025-07-29 12:06:45 | ERROR    | app.agents.test_case.test_case_generator_agent:_ai_generate_detailed_test_case_content:626 | 原始AI响应: ```json
{
    "preconditions": "1. 系统已部署并运行正常 2. 测试用户账户已创建并激活 3. 网络连接稳定 4. 浏览器或客户端应用已安装并配置正确 5. 测试环境与生产环境隔离",
    "test_steps": [
        {
            "step_number": 1,
            "action": "打开系统登录页面",
            "input_data": "N/A",
            "expected_result": "登录页面正确加载，显示用户名和密码输入框",
            "notes": "验证页面元素加载完整"
        },
        {
            "step_number": 2,
            "action": "输入有效的用户名和密码",
            "input_data": "用户名: <EMAIL>, 密码: Test@1234",
            "exp
2025-07-29 12:06:45 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:07:20 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:17:47 | ERROR    | app.agents.test_case.test_case_generator_agent:_run_ai_test_case_generation:930 | AI测试用例生成执行失败: 
2025-07-29 12:17:48 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:17:49 | ERROR    | app.agents.test_case.test_case_generator_agent:_run_ai_test_case_generation:930 | AI测试用例生成执行失败: Connection error.
2025-07-29 12:17:49 | INFO     | app.agents.factory:create_assistant_agent:124 | 创建 AssistantAgent: enterprise_test_case_generator (模型: deepseek)
2025-07-29 12:17:51 | ERROR    | app.agents.test_case.test_case_generator_agent:_run_ai_test_case_generation:930 | AI测试用例生成执行失败: Connection error.
2025-07-29 12:17:51 | ERROR    | app.agents.test_case.test_case_generator_agent:_analyze_test_coverage:1101 | 分析测试覆盖度失败: 'NoneType' object has no attribute 'lower'
2025-07-29 12:17:51 | INFO     | app.agents.test_case.test_case_generator_agent:_generatetest_cases_from_test_points:371 | 成功生成了 14 个企业级测试用例
2025-07-29 12:17:51 | INFO     | app.agents.test_case.test_case_generator_agent:_sendsave_request:1292 | 已发送企业级保存请求到数据库智能体: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:51 | INFO     | app.core.agents.base:__init__:47 | 初始化 测试用例保存智能体 智能体 (ID: test_case_saver)
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:__init__:108 | 测试用例保存智能体初始化完成: 测试用例保存智能体
2025-07-29 12:17:51 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 测试用例保存智能体
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:handle_test_case_save_request:130 | 开始处理测试用例保存请求: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 04f40386-e207-4af0-a0bf-57be6455772f
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 基础登录功能验证 - 有效email+正确密码
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 b78e5504-2b18-43ef-934b-fa69099b60e5
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 基础登录功能验证 - 有效email+错误密码
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 fa98622b-6630-4a1b-b0fe-69d65010fb39
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 基础登录功能验证 - 无效email格式+任意密码
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 40ac8e23-153b-4fe1-8718-cfc6e2c72811
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 验证码业务流程测试 - 提交正确验证码
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 8dac6058-46c8-4552-b8ff-f9a9f4bcf879
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 验证码业务流程测试 - 提交错误验证码
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 a1bca865-2779-462d-94df-0b0d29a1c7a0
2025-07-29 12:17:51 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 验证码业务流程测试 - 缺失验证码参数
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 b16245e4-d875-4ed9-b613-32d4b60c9c52
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 高并发登录性能测试
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 0debb140-9dc6-448a-b2cc-f938b40ad57e
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 敏感数据安全测试
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 6e5384e6-5fdb-44bb-9b39-cfc065e47793
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 认证服务与用户数据集成
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 4f2a6bf2-674e-43ea-a085-f5e4d86a6481
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 终端用户登录体验测试
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 336cac7e-cdd4-4ce6-94d8-4440e3ddbda0
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 输入字段边界测试
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 f22975c9-75e6-46d7-96e9-1916949f906b
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 服务异常容错测试 - 用户查询超时
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 80ef5359-41a6-4e8a-b6a7-6837c507fb77
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 服务异常容错测试 - token存储失败
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_requirements:1029 | 成功关联 5 个需求到测试用例 93178d15-4c87-459f-8f9e-ef95d9c87cc0
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_case_batch:411 | 成功保存测试用例: 服务异常容错测试 - 验证码服务500错误
2025-07-29 12:17:52 | INFO     | app.agents.database.test_case_saver_agent:_save_test_cases_to_database:335 | 批量保存完成: 成功 14 个，失败 0 个
2025-07-29 12:17:52 | INFO     | app.agents.test_case.test_case_generator_agent:_sendmind_map_request:1341 | 已发送企业级思维导图生成请求: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | INFO     | app.core.agents.base:__init__:47 | 初始化 思维导图生成智能体 智能体 (ID: mind_map_generator)
2025-07-29 12:17:52 | INFO     | app.agents.test_case.mind_map_generator_agent:__init__:90 | 思维导图生成智能体初始化完成: 思维导图生成智能体
2025-07-29 12:17:52 | INFO     | app.agents.factory:create_agent:171 | 创建智能体: 思维导图生成智能体
2025-07-29 12:17:52 | INFO     | app.agents.test_case.mind_map_generator_agent:handle_mind_map_generation_request:113 | 开始处理思维导图生成请求: d29774a0-6d5a-4fd0-b00c-950898c40fe3
2025-07-29 12:17:52 | INFO     | app.agents.test_case.mind_map_generator_agent:_fetch_test_cases_data:255 | 获取到 14 个测试用例数据
2025-07-29 12:17:52 | INFO     | app.agents.test_case.mind_map_generator_agent:_generate_mind_map:304 | 思维导图生成完成: 18 节点, 17 边
2025-07-29 12:17:52 | INFO     | app.agents.test_case.mind_map_generator_agent:_save_mind_map_to_database:597 | 思维导图已保存到数据库: 59012aef-73e5-4e91-b244-efe0edb1c3d3
2025-07-29 12:17:52 | WARNING  | app.agents.database.session_status_agent:update_session_status:486 | 运行时未初始化，直接调用数据库更新
2025-07-29 15:43:28 | INFO     | app.agents.factory:cleanup:433 | 清理智能体工厂资源...
2025-07-29 15:43:28 | INFO     | app.agents.factory:cleanup:435 | 智能体工厂资源清理完成
